import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    Card<PERSON>ooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from '@/hooks/use-auth';
import { useAuthApi } from '@/hooks/use-auth-api';
import { COMMON_RULES, validateForm } from '@/utils/form-validation';
import { Lock, Mail, Phone, User } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

const Signup = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { signUp } = useAuth();
  const { register, isLoading } = useAuthApi();
  const [userData, setUserData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    referralCode: searchParams.get('ref') || '',
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setUserData(prev => ({ ...prev, [id]: value }));
  };

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      navigate('/dashboard');
    }
  }, [navigate]);

  const handleEmailSignup = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form using validation rules
    const validationRules = {
      firstName: COMMON_RULES.firstName,
      lastName: COMMON_RULES.lastName,
      email: COMMON_RULES.email,
      phone: { ...COMMON_RULES.phone, required: false },
      password: COMMON_RULES.password,
      confirmPassword: {
        ...COMMON_RULES.confirmPassword,
        custom: (value: string) => {
          if (value !== userData.password) {
            return 'Passwords do not match';
          }
          return null;
        }
      },
    };

    const validationErrors = validateForm(userData, validationRules);

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors({});

    try {
      const result = await register({
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        password: userData.password,
        phone: userData.phone || undefined,
        referralCode: userData.referralCode || undefined,
      });

      if (result.data) {
        // Registration successful, navigate to dashboard
        navigate('/dashboard');
      }
    } catch (error: any) {
      console.error("Signup error:", error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-muted/50 to-background mobile-safe-area">
      <div className="w-full max-w-md lg:max-w-lg xl:max-w-xl">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-2">
            <div className="h-12 w-12 rounded-full overflow-hidden ring-2 ring-primary/20 bg-white/5 backdrop-blur-sm">
              <img 
                src="/lovable-uploads/532686f6-d477-4fff-934c-df00f9c4a11d.png" 
                alt="Better Interest Logo" 
                className="h-full w-full object-contain p-1"
              />
            </div>
            <h1 className="text-3xl font-bold text-primary">
              Better Interest
            </h1>
          </div>
          <p className="text-muted-foreground">Secure Digital Savings with Better Returns</p>
        </div>
        
        <Card className="w-full animate-scale-in">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Create an account</CardTitle>
            <CardDescription className="text-center">
              Enter your information to create your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleEmailSignup} className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="firstName"
                      value={userData.firstName}
                      onChange={handleInputChange}
                      placeholder="John"
                      required
                      className="pl-10"
                    />
                  </div>
                  {errors.firstName && (
                    <p className="text-red-500 text-sm">{errors.firstName}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="lastName"
                      value={userData.lastName}
                      onChange={handleInputChange}
                      placeholder="Doe"
                      required
                      className="pl-10"
                    />
                  </div>
                  {errors.lastName && (
                    <p className="text-red-500 text-sm">{errors.lastName}</p>
                  )}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    value={userData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    required
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number (Optional)</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    type="tel"
                    value={userData.phone}
                    onChange={handleInputChange}
                    placeholder="+234 ************"
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    value={userData.password}
                    onChange={handleInputChange}
                    placeholder="••••••••"
                    required
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={userData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="••••••••"
                    required
                    className="pl-10"
                  />
                </div>
              </div>
              <Button
                type="submit"
                className="mobile-button"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <span className="animate-spin mr-2">⟳</span>
                    Creating account...
                  </>
                ) : (
                  "Create Account"
                )}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <div className="text-center text-sm text-muted-foreground">
              Already have an account?{" "}
              <Button 
                variant="link" 
                className="text-brand-blue p-0 h-auto hover:underline"
                onClick={() => navigate('/login')}
              >
                Sign in
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Signup;
