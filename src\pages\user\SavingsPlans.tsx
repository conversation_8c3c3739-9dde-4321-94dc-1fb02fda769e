import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  PiggyBank, 
  Target, 
  TrendingUp, 
  Plus, 
  Clock,
  DollarSign,
  Users,
  Wallet
} from "lucide-react";
import { toast } from "sonner";

// Mock data for savings plans
const mockSavingsPlans = [
  {
    id: "1",
    name: "Emergency Fund",
    targetAmount: 500000,
    currentAmount: 125000,
    monthlyContribution: 25000,
    duration: 20,
    interestRate: 15,
    status: "active",
    type: "individual"
  },
  {
    id: "2", 
    name: "Vacation Fund",
    targetAmount: 200000,
    currentAmount: 80000,
    monthlyContribution: 15000,
    duration: 8,
    interestRate: 12,
    status: "active",
    type: "individual"
  }
];

const SavingsPlans = () => {
  const [savingsPlans] = useState(mockSavingsPlans);

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleCreatePlan = () => {
    toast.info("Create new savings plan feature coming soon!");
  };

  const handleViewPlan = (planId: string) => {
    toast.info(`Viewing plan ${planId} - Feature coming soon!`);
  };

  const handleDeposit = (planId: string) => {
    toast.info(`Deposit to plan ${planId} - Feature coming soon!`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Savings Plans</h1>
          <p className="text-muted-foreground">
            Manage your savings goals and track your progress
          </p>
        </div>
        <Button onClick={handleCreatePlan} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create New Plan
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Saved</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₦205,000</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Plans</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">
              Individual savings plans
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Interest Earned</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₦15,250</div>
            <p className="text-xs text-muted-foreground">
              13.5% average rate
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Savings Plans List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {savingsPlans.map((plan) => {
          const progress = calculateProgress(plan.currentAmount, plan.targetAmount);
          
          return (
            <Card key={plan.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {plan.type === 'group' ? (
                      <Users className="h-5 w-5 text-primary" />
                    ) : (
                      <PiggyBank className="h-5 w-5 text-primary" />
                    )}
                    <CardTitle className="text-lg">{plan.name}</CardTitle>
                  </div>
                  <Badge variant={plan.status === 'active' ? 'default' : 'secondary'}>
                    {plan.status}
                  </Badge>
                </div>
                <CardDescription>
                  {plan.type === 'group' ? 'Group Savings Plan' : 'Individual Savings Plan'}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>{progress.toFixed(1)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{formatCurrency(plan.currentAmount)}</span>
                    <span>{formatCurrency(plan.targetAmount)}</span>
                  </div>
                </div>

                {/* Plan Details */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-muted-foreground">Monthly</p>
                      <p className="font-medium">{formatCurrency(plan.monthlyContribution)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-muted-foreground">Duration</p>
                      <p className="font-medium">{plan.duration} months</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-muted-foreground">Interest</p>
                      <p className="font-medium">{plan.interestRate}% p.a.</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-muted-foreground">Remaining</p>
                      <p className="font-medium">
                        {formatCurrency(plan.targetAmount - plan.currentAmount)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleViewPlan(plan.id)}
                    className="flex-1"
                  >
                    View Details
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={() => handleDeposit(plan.id)}
                    className="flex-1"
                  >
                    Make Deposit
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Coming Soon Features */}
      <Card className="text-center py-8">
        <CardContent>
          <h3 className="text-lg font-semibold mb-2">More Savings Options Coming Soon!</h3>
          <p className="text-muted-foreground mb-4">
            We're working on exciting new features like Fixed Deposits, SafeLock, and AutoSave.
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <Badge variant="outline">Fixed Deposits</Badge>
            <Badge variant="outline">SafeLock</Badge>
            <Badge variant="outline">AutoSave</Badge>
            <Badge variant="outline">Round-Up Savings</Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SavingsPlans;
