const mongoose = require('mongoose');

const SafeLockSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 5000, // Minimum ₦5,000
  },
  lockDuration: {
    type: Number,
    required: true,
    min: 30, // Minimum 30 days (1 month)
    max: 365, // Maximum 365 days (12 months)
  },
  interestRate: {
    type: Number,
    required: true,
    default: 20, // 20% annual interest rate (higher than fixed deposit)
  },
  startDate: {
    type: Date,
    required: true,
    default: Date.now,
  },
  maturityDate: {
    type: Date,
    required: true,
  },
  status: {
    type: String,
    enum: ['active', 'matured', 'broken'],
    default: 'active',
  },
  // Emergency break settings
  canBreak: {
    type: Boolean,
    default: true, // Allow emergency break
  },
  breakagePenalty: {
    type: Number,
    default: 50, // 50% of principal penalty for emergency break
  },
  // Maturity bonus
  maturityBonus: {
    type: Number,
    default: 2, // 2% bonus on completion
  },
  // Calculated values
  accruedInterest: {
    type: Number,
    default: 0,
  },
  totalMaturityValue: {
    type: Number,
    default: 0,
  },
  // Break details (if applicable)
  brokenAt: {
    type: Date,
    default: null,
  },
  breakReason: {
    type: String,
    default: null,
  },
  penaltyAmount: {
    type: Number,
    default: 0,
  },
  amountReceived: {
    type: Number,
    default: 0,
  },
  // Maturity processing
  maturityProcessed: {
    type: Boolean,
    default: false,
  },
  maturityProcessedAt: {
    type: Date,
    default: null,
  },
  // Reference for tracking
  reference: {
    type: String,
    unique: true,
    required: true,
  },
  // Lock tier (affects interest rate)
  lockTier: {
    type: String,
    enum: ['bronze', 'silver', 'gold', 'platinum'],
    default: 'bronze',
  },
}, { timestamps: true });

// Pre-save middleware to calculate maturity date, tier, and generate reference
SafeLockSchema.pre('save', function(next) {
  if (this.isNew) {
    // Calculate maturity date
    this.maturityDate = new Date(this.startDate.getTime() + (this.lockDuration * 24 * 60 * 60 * 1000));
    
    // Determine lock tier and interest rate based on amount and duration
    this.lockTier = this.calculateLockTier();
    this.interestRate = this.calculateInterestRate();
    
    // Calculate total maturity value
    this.totalMaturityValue = this.calculateMaturityValue();
    
    // Generate unique reference
    if (!this.reference) {
      const crypto = require('crypto');
      this.reference = 'SL' + crypto.randomBytes(6).toString('hex').toUpperCase();
    }
  }
  next();
});

// Method to calculate lock tier based on amount and duration
SafeLockSchema.methods.calculateLockTier = function() {
  const amount = this.amount;
  const duration = this.lockDuration;
  
  if (amount >= 1000000 && duration >= 365) return 'platinum'; // ₦1M+ for 1 year
  if (amount >= 500000 && duration >= 180) return 'gold';      // ₦500K+ for 6 months
  if (amount >= 100000 && duration >= 90) return 'silver';     // ₦100K+ for 3 months
  return 'bronze';                                             // Default tier
};

// Method to calculate interest rate based on tier and duration
SafeLockSchema.methods.calculateInterestRate = function() {
  const duration = this.lockDuration;
  let baseRate = 18; // Base 18% annual
  
  // Duration bonuses
  if (duration >= 90) baseRate += 1;   // 19% for 3+ months
  if (duration >= 180) baseRate += 1;  // 20% for 6+ months
  if (duration >= 365) baseRate += 2;  // 22% for 1+ year
  
  // Tier bonuses
  const tierBonuses = {
    bronze: 0,
    silver: 1,
    gold: 2,
    platinum: 3
  };
  
  return baseRate + tierBonuses[this.lockTier];
};

// Method to calculate total maturity value including bonus
SafeLockSchema.methods.calculateMaturityValue = function() {
  const dailyRate = this.interestRate / 365 / 100;
  const compoundInterest = this.amount * Math.pow(1 + dailyRate, this.lockDuration) - this.amount;
  const bonus = this.amount * (this.maturityBonus / 100);
  
  return this.amount + compoundInterest + bonus;
};

// Method to calculate current accrued interest
SafeLockSchema.methods.calculateAccruedInterest = function() {
  const now = new Date();
  const startDate = this.startDate;
  const daysElapsed = Math.floor((now - startDate) / (1000 * 60 * 60 * 24));
  
  if (daysElapsed <= 0) return 0;
  
  const dailyRate = this.interestRate / 365 / 100;
  const accruedInterest = this.amount * Math.pow(1 + dailyRate, daysElapsed) - this.amount;
  
  return Math.round(accruedInterest * 100) / 100;
};

// Method to calculate emergency break penalty
SafeLockSchema.methods.calculateBreakPenalty = function() {
  if (!this.canBreak) return this.amount; // Lose everything if break not allowed
  
  return Math.round(this.amount * (this.breakagePenalty / 100) * 100) / 100;
};

// Method to check if SafeLock has matured
SafeLockSchema.methods.hasMatured = function() {
  return new Date() >= this.maturityDate;
};

// Method to process emergency break
SafeLockSchema.methods.processEmergencyBreak = async function(reason = 'Emergency break by user') {
  if (this.status !== 'active') {
    throw new Error('SafeLock is not active');
  }
  
  if (!this.canBreak) {
    throw new Error('Emergency break not allowed for this SafeLock');
  }
  
  const penalty = this.calculateBreakPenalty();
  const amountReceived = this.amount - penalty;
  
  this.status = 'broken';
  this.brokenAt = new Date();
  this.breakReason = reason;
  this.penaltyAmount = penalty;
  this.amountReceived = Math.max(0, amountReceived); // Ensure non-negative
  this.accruedInterest = 0; // No interest on broken SafeLock
  
  await this.save();
  return this.amountReceived;
};

// Method to process maturity
SafeLockSchema.methods.processMaturity = async function() {
  if (!this.hasMatured() || this.maturityProcessed) {
    return false;
  }
  
  this.accruedInterest = this.calculateAccruedInterest();
  const bonus = this.amount * (this.maturityBonus / 100);
  this.totalMaturityValue = this.amount + this.accruedInterest + bonus;
  this.status = 'matured';
  this.maturityProcessed = true;
  this.maturityProcessedAt = new Date();
  
  await this.save();
  return true;
};

// Static method to get user's active SafeLocks
SafeLockSchema.statics.getUserActiveSafeLocks = function(userId) {
  return this.find({ userId, status: 'active' }).sort({ createdAt: -1 });
};

// Static method to get SafeLocks due for maturity processing
SafeLockSchema.statics.getSafeLocksForMaturityProcessing = function() {
  return this.find({
    status: 'active',
    maturityDate: { $lte: new Date() },
    maturityProcessed: false
  });
};

// Static method to get interest rates for different tiers
SafeLockSchema.statics.getInterestRates = function() {
  return {
    bronze: { base: 18, description: 'Standard rate for amounts under ₦100,000' },
    silver: { base: 19, description: '₦100,000+ for 3+ months' },
    gold: { base: 20, description: '₦500,000+ for 6+ months' },
    platinum: { base: 21, description: '₦1,000,000+ for 1+ year' },
    durationBonuses: {
      '90+': 1,
      '180+': 2,
      '365+': 4
    },
    maturityBonus: 2
  };
};

// Static method to calculate projected returns
SafeLockSchema.statics.calculateProjectedReturns = function(amount, duration) {
  // Create temporary instance to use instance methods
  const tempSafeLock = new this({
    amount: parseFloat(amount),
    lockDuration: parseInt(duration),
    startDate: new Date()
  });

  const tier = tempSafeLock.calculateLockTier();
  const interestRate = tempSafeLock.calculateInterestRate();
  const maturityValue = tempSafeLock.calculateMaturityValue();

  return {
    principal: parseFloat(amount),
    duration: parseInt(duration),
    tier,
    interestRate,
    maturityValue: Math.round(maturityValue * 100) / 100,
    totalReturns: Math.round((maturityValue - amount) * 100) / 100,
    maturityBonus: Math.round((amount * 0.02) * 100) / 100
  };
};

module.exports = mongoose.model('SafeLock', SafeLockSchema);
