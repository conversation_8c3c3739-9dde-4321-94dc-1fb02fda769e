const express = require('express');
const router = express.Router();
const Referral = require('../models/referral');
const User = require('../models/user');
const { authenticateToken } = require('../middleware/authMiddleware');

// POST /api/referral/use-code - Use a referral code during registration
router.post('/use-code', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { referralCode, ipAddress, deviceFingerprint } = req.body;

    if (!referralCode) {
      return res.status(400).json({ 
        success: false, 
        message: 'Referral code is required' 
      });
    }

    // Check if user already used a referral code
    const existingReferral = await Referral.findOne({ refereeId: userId });
    if (existingReferral) {
      return res.status(400).json({ 
        success: false, 
        message: 'You have already used a referral code' 
      });
    }

    // Find the referrer by referral code
    const referrer = await User.findOne({ referralCode });
    if (!referrer) {
      return res.status(404).json({ 
        success: false, 
        message: 'Invalid referral code' 
      });
    }

    // Check if user is trying to refer themselves
    if (referrer._id.toString() === userId) {
      return res.status(400).json({ 
        success: false, 
        message: 'You cannot refer yourself' 
      });
    }

    // Get referrer's tier and multiplier
    const { tier, multiplier } = await Referral.calculateReferrerTier(referrer._id);

    // Create referral record
    const referral = new Referral({
      referrerId: referrer._id,
      refereeId: userId,
      referralCode,
      ipAddress,
      deviceFingerprint,
      referrerTier: tier,
      tierMultiplier: multiplier,
    });

    await referral.save();

    // Update user's referredBy field
    await User.findByIdAndUpdate(userId, { referredBy: referrer._id });

    // Update referrer's total referrals count
    await User.findByIdAndUpdate(referrer._id, { $inc: { totalReferrals: 1 } });

    // Check for suspicious activity
    const suspiciousReasons = await referral.detectSuspiciousActivity();

    res.status(201).json({
      success: true,
      message: 'Referral code applied successfully',
      data: {
        referral: {
          id: referral._id,
          referrerName: `${referrer.firstName} ${referrer.lastName}`,
          status: referral.status,
          potentialReward: referral.refereeReward,
          qualificationCriteria: referral.qualificationCriteria
        },
        suspicious: suspiciousReasons.length > 0,
        suspiciousReasons
      }
    });

  } catch (error) {
    console.error('Error using referral code:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/referral/stats/:userId - Get user's referral statistics
router.get('/stats/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user can access this data
    if (req.user.id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied' 
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Get referral statistics
    const stats = await Referral.getUserReferralStats(userId);

    // Get recent referrals
    const recentReferrals = await Referral.find({ referrerId: userId })
      .populate('refereeId', 'firstName lastName createdAt')
      .sort({ createdAt: -1 })
      .limit(10);

    // Get pending referrals that need qualification check
    const pendingReferrals = await Referral.find({ 
      referrerId: userId, 
      status: 'pending' 
    }).populate('refereeId', 'firstName lastName');

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: `${user.firstName} ${user.lastName}`,
          referralCode: user.referralCode,
          totalReferrals: user.totalReferrals,
          referralEarnings: user.referralEarnings
        },
        stats,
        recentReferrals: recentReferrals.map(ref => ({
          id: ref._id,
          refereeName: `${ref.refereeId.firstName} ${ref.refereeId.lastName}`,
          status: ref.status,
          createdAt: ref.createdAt,
          qualificationDate: ref.qualificationDate,
          rewardAmount: ref.referrerReward * ref.tierMultiplier
        })),
        pendingReferrals: pendingReferrals.map(ref => ({
          id: ref._id,
          refereeName: `${ref.refereeId.firstName} ${ref.refereeId.lastName}`,
          qualificationCriteria: ref.qualificationCriteria,
          expiryDate: ref.expiryDate
        }))
      }
    });

  } catch (error) {
    console.error('Error fetching referral stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/referral/leaderboard - Get referral leaderboard
router.get('/leaderboard', authenticateToken, async (req, res) => {
  try {
    const { period = 'all', limit = 10 } = req.query;

    let matchCondition = { status: 'rewarded' };
    
    // Add time filter based on period
    if (period !== 'all') {
      let startDate = new Date();
      switch (period) {
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
      }
      matchCondition.rewardPaidDate = { $gte: startDate };
    }

    const leaderboard = await Referral.aggregate([
      { $match: matchCondition },
      {
        $group: {
          _id: '$referrerId',
          totalReferrals: { $sum: 1 },
          totalEarnings: { $sum: { $multiply: ['$referrerReward', '$tierMultiplier'] } },
          lastReferralDate: { $max: '$rewardPaidDate' }
        }
      },
      { $sort: { totalReferrals: -1, totalEarnings: -1 } },
      { $limit: parseInt(limit) },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          userId: '$_id',
          name: { $concat: ['$user.firstName', ' ', '$user.lastName'] },
          totalReferrals: 1,
          totalEarnings: 1,
          lastReferralDate: 1,
          tier: '$user.tier'
        }
      }
    ]);

    // Add tier information for each user
    for (let entry of leaderboard) {
      const { tier } = await Referral.calculateReferrerTier(entry.userId);
      entry.currentTier = tier;
    }

    res.json({
      success: true,
      data: {
        period,
        leaderboard,
        tiers: {
          bronze: { minReferrals: 0, multiplier: 1 },
          silver: { minReferrals: 10, multiplier: 1.5 },
          gold: { minReferrals: 20, multiplier: 2 },
          platinum: { minReferrals: 50, multiplier: 2.5 }
        }
      }
    });

  } catch (error) {
    console.error('Error fetching referral leaderboard:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/referral/check-qualification - Check and update referral qualifications
router.post('/check-qualification', authenticateToken, async (req, res) => {
  try {
    // This endpoint should be called by cron jobs or admin
    const user = req.user;
    if (user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied. Admin privileges required.' 
      });
    }

    const pendingReferrals = await Referral.getReferralsForProcessing();
    const results = {
      processed: 0,
      qualified: 0,
      rewarded: 0,
      errors: []
    };

    for (const referral of pendingReferrals) {
      try {
        results.processed++;
        
        const isQualified = await referral.checkQualification();
        
        if (isQualified) {
          results.qualified++;
          
          // Process reward if not suspicious
          if (!referral.isSuspicious) {
            await referral.processReward();
            results.rewarded++;
          }
        }
        
      } catch (error) {
        console.error(`Error processing referral ${referral._id}:`, error);
        results.errors.push({
          referralId: referral._id,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      message: 'Referral qualification check completed',
      data: results
    });

  } catch (error) {
    console.error('Error checking referral qualifications:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/referral/my-code - Get user's referral code and sharing info
router.get('/my-code', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Generate sharing links and messages
    const baseUrl = process.env.FRONTEND_URL || 'https://kojapay.com';
    const referralLink = `${baseUrl}/register?ref=${user.referralCode}`;
    
    const shareMessage = `Join me on KojaPay and start saving smartly! Use my referral code ${user.referralCode} and get ₦200 bonus when you save ₦5,000. Sign up here: ${referralLink}`;

    res.json({
      success: true,
      data: {
        referralCode: user.referralCode,
        referralLink,
        shareMessage,
        rewards: {
          referrerReward: 500,
          refereeReward: 200,
          qualificationCriteria: {
            kycRequired: true,
            minimumSavings: 5000,
            minimumActiveDays: 7
          }
        },
        socialSharing: {
          whatsapp: `https://wa.me/?text=${encodeURIComponent(shareMessage)}`,
          twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareMessage)}`,
          facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(referralLink)}`
        }
      }
    });

  } catch (error) {
    console.error('Error fetching referral code:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

module.exports = router;
