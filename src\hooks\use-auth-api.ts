import { useCallback, useState } from 'react';
import { useApiErrorHandler } from './use-api-error-handler';

interface LoginData {
  email: string;
  password: string;
}

interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone?: string;
  referralCode?: string;
}

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: string;
  balance: number;
  savingsBalance: number;
  totalEarnings: number;
  kycStatus: string;
  referralCode: string;
  isActive: boolean;
  createdAt: string;
}

interface AuthResponse {
  user: User;
  token: string;
  message: string;
}

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export function useAuthApi() {
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const { handleError, handleSuccess } = useApiErrorHandler();

  // Login function
  const login = useCallback(async (loginData: LoginData) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
      }

      const result: AuthResponse = await response.json();
      
      // Store token and user data
      localStorage.setItem('token', result.token);
      localStorage.setItem('user', JSON.stringify(result.user));
      setUser(result.user);

      handleSuccess(`Welcome back, ${result.user.firstName}!`);
      
      return { data: result, error: null };
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [handleError, handleSuccess]);

  // Register function
  const register = useCallback(async (registerData: RegisterData) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registerData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Registration failed');
      }

      const result: AuthResponse = await response.json();
      
      // Store token and user data
      localStorage.setItem('token', result.token);
      localStorage.setItem('user', JSON.stringify(result.user));
      setUser(result.user);

      handleSuccess(`Welcome to KojaPay, ${result.user.firstName}!`);
      
      return { data: result, error: null };
    } catch (error) {
      console.error('Registration error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [handleError, handleSuccess]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      
      if (token) {
        // Optional: Call logout endpoint to invalidate token on server
        await fetch(`${API_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call success
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      setUser(null);
      handleSuccess('Logged out successfully');
    }
  }, [handleSuccess]);

  // Get current user profile
  const getCurrentUser = useCallback(async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('No authentication token found');

      const response = await fetch(`${API_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch user profile');
      }

      const result = await response.json();
      setUser(result.data);
      
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Get user error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user profile';
      
      // If token is invalid, clear it
      if (errorMessage.includes('token') || errorMessage.includes('authentication')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        setUser(null);
      }
      
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update user profile
  const updateProfile = useCallback(async (updateData: Partial<User>) => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('Authentication required');

      const response = await fetch(`${API_URL}/auth/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update profile');
      }

      const result = await response.json();
      setUser(result.data);
      localStorage.setItem('user', JSON.stringify(result.data));

      handleSuccess('Profile updated successfully!');
      
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Update profile error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [handleError, handleSuccess]);

  // Change password
  const changePassword = useCallback(async (currentPassword: string, newPassword: string) => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) throw new Error('Authentication required');

      const response = await fetch(`${API_URL}/auth/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to change password');
      }

      handleSuccess('Password changed successfully!');
      
      return { data: true, error: null };
    } catch (error) {
      console.error('Change password error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to change password';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [handleError, handleSuccess]);

  // Forgot password
  const forgotPassword = useCallback(async (email: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send reset email');
      }

      handleSuccess('Password reset email sent! Check your inbox.');
      
      return { data: true, error: null };
    } catch (error) {
      console.error('Forgot password error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [handleError, handleSuccess]);

  // Reset password
  const resetPassword = useCallback(async (token: string, newPassword: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          newPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to reset password');
      }

      handleSuccess('Password reset successfully! You can now log in with your new password.');
      
      return { data: true, error: null };
    } catch (error) {
      console.error('Reset password error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset password';
      handleError(error, errorMessage);
      return { data: null, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [handleError, handleSuccess]);

  // Initialize user from localStorage
  const initializeAuth = useCallback(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        return true;
      } catch (error) {
        console.error('Error parsing user data:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    }
    return false;
  }, []);

  return {
    isLoading,
    user,
    login,
    register,
    logout,
    getCurrentUser,
    updateProfile,
    changePassword,
    forgotPassword,
    resetPassword,
    initializeAuth,
  };
}

export default useAuthApi;
