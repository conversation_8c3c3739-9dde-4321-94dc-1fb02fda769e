const mongoose = require('mongoose');

const FlexSavingsSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true, // One flex account per user
  },
  balance: {
    type: Number,
    default: 0,
    min: 0,
  },
  dailyInterestRate: {
    type: Number,
    default: 0.0411, // 15% annually / 365 days = 0.0411% daily
  },
  lastInterestCalculation: {
    type: Date,
    default: Date.now,
  },
  totalInterestEarned: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  // Track daily balances for interest calculation
  dailyBalances: [{
    date: {
      type: Date,
      required: true,
    },
    balance: {
      type: Number,
      required: true,
    },
    interestEarned: {
      type: Number,
      default: 0,
    }
  }],
  // Monthly interest summary
  monthlyInterest: [{
    month: {
      type: String, // Format: "2024-01"
      required: true,
    },
    totalInterest: {
      type: Number,
      required: true,
    },
    averageBalance: {
      type: Number,
      required: true,
    }
  }],
}, { timestamps: true });

// Index for efficient queries
FlexSavingsSchema.index({ userId: 1 });
FlexSavingsSchema.index({ lastInterestCalculation: 1 });

// Virtual for annual interest rate
FlexSavingsSchema.virtual('annualInterestRate').get(function() {
  return this.dailyInterestRate * 365;
});

// Method to calculate daily interest
FlexSavingsSchema.methods.calculateDailyInterest = function() {
  if (this.balance <= 0) return 0;
  return this.balance * (this.dailyInterestRate / 100);
};

// Method to add daily interest
FlexSavingsSchema.methods.addDailyInterest = function() {
  const interest = this.calculateDailyInterest();
  
  if (interest > 0) {
    this.balance += interest;
    this.totalInterestEarned += interest;
    
    // Add to daily balances tracking
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    this.dailyBalances.push({
      date: today,
      balance: this.balance - interest, // Balance before interest
      interestEarned: interest
    });
    
    // Keep only last 365 days of records
    if (this.dailyBalances.length > 365) {
      this.dailyBalances = this.dailyBalances.slice(-365);
    }
    
    this.lastInterestCalculation = new Date();
  }
  
  return interest;
};

// Method to deposit money
FlexSavingsSchema.methods.deposit = function(amount) {
  if (amount <= 0) {
    throw new Error('Deposit amount must be positive');
  }
  
  this.balance += amount;
  return this.balance;
};

// Method to withdraw money
FlexSavingsSchema.methods.withdraw = function(amount) {
  if (amount <= 0) {
    throw new Error('Withdrawal amount must be positive');
  }
  
  if (amount > this.balance) {
    throw new Error('Insufficient balance');
  }
  
  this.balance -= amount;
  return this.balance;
};

// Method to get interest earned in a specific month
FlexSavingsSchema.methods.getMonthlyInterest = function(year, month) {
  const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
  const monthData = this.monthlyInterest.find(m => m.month === monthKey);
  return monthData ? monthData.totalInterest : 0;
};

// Method to update monthly interest summary
FlexSavingsSchema.methods.updateMonthlyInterest = function() {
  const now = new Date();
  const currentMonth = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
  
  // Calculate interest for current month
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const dailyRecords = this.dailyBalances.filter(record => 
    record.date >= startOfMonth
  );
  
  if (dailyRecords.length > 0) {
    const totalInterest = dailyRecords.reduce((sum, record) => sum + record.interestEarned, 0);
    const averageBalance = dailyRecords.reduce((sum, record) => sum + record.balance, 0) / dailyRecords.length;
    
    // Update or create monthly record
    const existingIndex = this.monthlyInterest.findIndex(m => m.month === currentMonth);
    if (existingIndex >= 0) {
      this.monthlyInterest[existingIndex] = {
        month: currentMonth,
        totalInterest,
        averageBalance
      };
    } else {
      this.monthlyInterest.push({
        month: currentMonth,
        totalInterest,
        averageBalance
      });
    }
    
    // Keep only last 12 months
    if (this.monthlyInterest.length > 12) {
      this.monthlyInterest = this.monthlyInterest.slice(-12);
    }
  }
};

// Static method to get or create flex account for user
FlexSavingsSchema.statics.getOrCreateForUser = async function(userId) {
  let flexAccount = await this.findOne({ userId });
  
  if (!flexAccount) {
    flexAccount = new this({ userId });
    await flexAccount.save();
  }
  
  return flexAccount;
};

// Static method to get all accounts for daily interest calculation
FlexSavingsSchema.statics.getAccountsForInterestCalculation = function() {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(23, 59, 59, 999);
  
  return this.find({
    isActive: true,
    balance: { $gt: 0 },
    lastInterestCalculation: { $lt: yesterday }
  });
};

module.exports = mongoose.model('FlexSavings', FlexSavingsSchema);
