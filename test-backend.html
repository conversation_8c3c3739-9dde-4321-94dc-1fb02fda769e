<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KojaPay Backend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #f44336;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 KojaPay Backend API Test</h1>
        
        <div class="test-section">
            <h3>📡 Backend Connection Test</h3>
            <button onclick="testConnection()">Test Backend Connection</button>
            <div id="connectionResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>👤 User Registration Test</h3>
            <input type="text" id="firstName" placeholder="First Name" value="John">
            <input type="text" id="lastName" placeholder="Last Name" value="Doe">
            <input type="email" id="email" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="Test123!@#">
            <input type="tel" id="phone" placeholder="Phone" value="+2348012345678">
            <br>
            <button onclick="testRegistration()">Test Registration</button>
            <div id="registrationResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔐 User Login Test</h3>
            <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
            <input type="password" id="loginPassword" placeholder="Password" value="Test123!@#">
            <br>
            <button onclick="testLogin()">Test Login</button>
            <div id="loginResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>💰 Savings API Test</h3>
            <button onclick="testFlexSavings()">Test Flex Savings</button>
            <button onclick="testFixedDeposit()">Test Fixed Deposit</button>
            <button onclick="testSafeLock()">Test SafeLock</button>
            <div id="savingsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>👨‍💼 Admin Login Test</h3>
            <input type="email" id="adminEmail" placeholder="Admin Email" value="<EMAIL>">
            <input type="password" id="adminPassword" placeholder="Admin Password" value="KojaPay2024Admin!">
            <br>
            <button onclick="testAdminLogin()">Test Admin Login</button>
            <div id="adminResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3001/api';
        let authToken = '';

        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            try {
                const response = await fetch(`${API_URL}/auth/test`, {
                    method: 'GET',
                });
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Backend is running successfully!\nStatus: ' + response.status;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Backend responded with error: ' + response.status;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Cannot connect to backend:\n' + error.message;
            }
        }

        async function testRegistration() {
            const resultDiv = document.getElementById('registrationResult');
            const userData = {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                phone: document.getElementById('phone').value
            };

            try {
                const response = await fetch(`${API_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Registration successful!\n' + JSON.stringify(data, null, 2);
                    authToken = data.token;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Registration failed:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Registration error:\n' + error.message;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            const loginData = {
                email: document.getElementById('loginEmail').value,
                password: document.getElementById('loginPassword').value
            };

            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Login successful!\n' + JSON.stringify(data, null, 2);
                    authToken = data.token;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Login failed:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Login error:\n' + error.message;
            }
        }

        async function testAdminLogin() {
            const resultDiv = document.getElementById('adminResult');
            const loginData = {
                email: document.getElementById('adminEmail').value,
                password: document.getElementById('adminPassword').value
            };

            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Admin login successful!\n' + JSON.stringify(data, null, 2);
                    authToken = data.token;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Admin login failed:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Admin login error:\n' + error.message;
            }
        }

        async function testFlexSavings() {
            const resultDiv = document.getElementById('savingsResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Please login first to test savings APIs';
                return;
            }

            try {
                const response = await fetch(`${API_URL}/flex-savings/balance`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Flex Savings API working!\n' + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Flex Savings API failed:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Flex Savings API error:\n' + error.message;
            }
        }

        async function testFixedDeposit() {
            const resultDiv = document.getElementById('savingsResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Please login first to test savings APIs';
                return;
            }

            try {
                const response = await fetch(`${API_URL}/fixed-deposit/user`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Fixed Deposit API working!\n' + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Fixed Deposit API failed:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Fixed Deposit API error:\n' + error.message;
            }
        }

        async function testSafeLock() {
            const resultDiv = document.getElementById('savingsResult');
            
            if (!authToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Please login first to test savings APIs';
                return;
            }

            try {
                const response = await fetch(`${API_URL}/safelock/user`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ SafeLock API working!\n' + JSON.stringify(data, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ SafeLock API failed:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ SafeLock API error:\n' + error.message;
            }
        }

        // Test connection on page load
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
