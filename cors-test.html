<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test - KojaPay</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .online { background: #4CAF50; }
        .offline { background: #f44336; }
    </style>
</head>
<body>
    <h1>🔧 CORS Test - KojaPay Backend</h1>
    
    <div class="test-container">
        <h3>Backend Status</h3>
        <div id="status">
            <span class="status offline"></span>
            <span>Checking...</span>
        </div>
        <button onclick="checkStatus()">Refresh Status</button>
    </div>

    <div class="test-container">
        <h3>CORS Tests</h3>
        <button onclick="testSimpleGET()">Test Simple GET</button>
        <button onclick="testPOSTWithCORS()">Test POST with CORS</button>
        <button onclick="testAuthLogin()">Test Auth Login</button>
        <div id="corsResults" class="result"></div>
    </div>

    <div class="test-container">
        <h3>Network Information</h3>
        <div id="networkInfo">
            <p><strong>Current Page:</strong> <span id="currentUrl"></span></p>
            <p><strong>Backend URL:</strong> http://localhost:3001/api</p>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:3001/api';
        
        // Update page info
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('userAgent').textContent = navigator.userAgent;

        async function checkStatus() {
            const statusDiv = document.getElementById('status');
            
            try {
                const response = await fetch(`${API_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.innerHTML = `
                        <span class="status online"></span>
                        <span>Backend Online - ${data.message}</span>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <span class="status offline"></span>
                        <span>Backend Error - Status: ${response.status}</span>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <span class="status offline"></span>
                    <span>Backend Offline - ${error.message}</span>
                `;
            }
        }

        async function testSimpleGET() {
            const resultDiv = document.getElementById('corsResults');
            resultDiv.textContent = 'Testing simple GET request...';
            
            try {
                const response = await fetch(`${API_URL}/test`, {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Simple GET Success!\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Simple GET Failed - Status: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Simple GET Error: ${error.message}`;
            }
        }

        async function testPOSTWithCORS() {
            const resultDiv = document.getElementById('corsResults');
            resultDiv.textContent = 'Testing POST request with CORS...';
            
            try {
                const response = await fetch(`${API_URL}/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ test: 'CORS POST test' })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ POST with CORS Success!\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ POST with CORS Failed - Status: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ POST with CORS Error: ${error.message}`;
            }
        }

        async function testAuthLogin() {
            const resultDiv = document.getElementById('corsResults');
            resultDiv.textContent = 'Testing Auth Login with CORS...';
            
            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'KojaPay2024Admin!'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Auth Login Success!\nUser: ${data.user.firstName} ${data.user.lastName}\nRole: ${data.user.role}`;
                } else {
                    const errorData = await response.json();
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Auth Login Failed - Status: ${response.status}\n${JSON.stringify(errorData, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Auth Login Error: ${error.message}`;
            }
        }

        // Check status on page load
        checkStatus();
        
        // Auto-refresh status every 10 seconds
        setInterval(checkStatus, 10000);
    </script>
</body>
</html>
