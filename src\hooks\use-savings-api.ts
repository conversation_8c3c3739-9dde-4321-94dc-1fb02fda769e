import axios from 'axios';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

// Types for new savings products
export interface FlexSavingsAccount {
  _id: string;
  userId: string;
  balance: number;
  dailyInterestRate: number;
  totalInterestEarned: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface FixedDeposit {
  _id: string;
  userId: string;
  amount: number;
  duration: number;
  interestRate: number;
  startDate: string;
  maturityDate: string;
  accruedInterest: number;
  status: 'active' | 'matured' | 'broken';
  reference: string;
}

export interface SafeLock {
  _id: string;
  userId: string;
  amount: number;
  lockDuration: number;
  interestRate: number;
  startDate: string;
  maturityDate: string;
  status: 'active' | 'matured' | 'broken';
  lockTier: 'bronze' | 'silver' | 'gold' | 'platinum';
  reference: string;
}

export interface Card {
  _id: string;
  userId: string;
  last4: string;
  brand: string;
  bank: string;
  isDefault: boolean;
  isVerified: boolean;
  maskedPan: string;
}

export interface AutoSave {
  _id: string;
  userId: string;
  cardId: string;
  frequency: 'daily' | 'weekly' | 'monthly';
  amount: number;
  nextDebitDate: string;
  isActive: boolean;
  isPaused: boolean;
  totalDebits: number;
  totalAmount: number;
}

import { API_CONFIG } from '@/config/env';

const API_URL = API_CONFIG.BASE_URL;

console.log('💰 Savings API Configuration:', {
  API_URL,
  BACKEND_URL: API_CONFIG.BACKEND_URL
});

export function useSavingsApi() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { handleError, handleSuccess } = useApiErrorHandler();

  // Helper to get token from localStorage
  const getToken = () => localStorage.getItem('token');

  // Flex Savings API calls
  const getFlexAccount = useCallback(async () => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.get(`${API_URL}/flex-savings/account`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error fetching flex account:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch flex account';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const depositToFlex = useCallback(async (amount: number) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/flex-savings/deposit`, 
        { amount },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('Deposit successful!');
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error depositing to flex:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to deposit';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  const withdrawFromFlex = useCallback(async (amount: number) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/flex-savings/withdraw`, 
        { amount },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('Withdrawal successful!');
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error withdrawing from flex:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to withdraw';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  // Fixed Deposit API calls
  const createFixedDeposit = useCallback(async (amount: number, duration: number, autoRenewal = false) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/fixed-deposit/create`, 
        { amount, duration, autoRenewal },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('Fixed deposit created successfully!');
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error creating fixed deposit:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to create fixed deposit';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  const getUserFixedDeposits = useCallback(async (status?: string) => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const params = status ? { status } : {};
      const response = await axios.get(`${API_URL}/fixed-deposit/user`, {
        headers: { Authorization: `Bearer ${token}` },
        params
      });
      
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error fetching fixed deposits:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch fixed deposits';
      
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const breakFixedDeposit = useCallback(async (depositId: string, reason?: string) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/fixed-deposit/break/${depositId}`, 
        { reason },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('Fixed deposit terminated successfully');
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error breaking fixed deposit:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to break fixed deposit';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  // SafeLock API calls
  const createSafeLock = useCallback(async (amount: number, lockDuration: number, canBreak = true) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/safelock/create`, 
        { amount, lockDuration, canBreak },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('SafeLock created successfully!');
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error creating SafeLock:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to create SafeLock';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  const getUserSafeLocks = useCallback(async (status?: string) => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const params = status ? { status } : {};
      const response = await axios.get(`${API_URL}/safelock/user`, {
        headers: { Authorization: `Bearer ${token}` },
        params
      });
      
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error fetching SafeLocks:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch SafeLocks';
      
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const breakSafeLock = useCallback(async (safeLockId: string, reason: string, confirmBreak: boolean) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/safelock/break/${safeLockId}`, 
        { reason, confirmBreak },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('SafeLock broken successfully');
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error breaking SafeLock:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to break SafeLock';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  // Card Management API calls
  const getUserCards = useCallback(async () => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.get(`${API_URL}/cards/user`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error fetching cards:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch cards';
      
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const addCard = useCallback(async (email: string) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/cards/add`, 
        { email },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      return { data: response.data.data, error: null };
    } catch (error) {
      console.error('Error adding card:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to add card';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  return {
    isLoading,
    isSaving,
    // Flex Savings
    getFlexAccount,
    depositToFlex,
    withdrawFromFlex,
    // Fixed Deposits
    createFixedDeposit,
    getUserFixedDeposits,
    breakFixedDeposit,
    // SafeLock
    createSafeLock,
    getUserSafeLocks,
    breakSafeLock,
    // Cards
    getUserCards,
    addCard,
  };
}
