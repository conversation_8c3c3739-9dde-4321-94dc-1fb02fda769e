// models/User.js
const mongoose = require ("mongoose");
const bcrypt = require ("bcryptjs");

const userSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  phoneNumber: {
    type: String,
    required: true,
    unique: true
  },
  password: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ["user", "admin", "staff"],
    default: "user",
    required: true,
    select: true
  },
  balance: {
    type: Number,
    default: 0,
  },
  // Enhanced balance tracking
  savingsBalance: {
    type: Number,
    default: 0,
  },
  investmentBalance: {
    type: Number,
    default: 0,
  },
  loanBalance: {
    type: Number,
    default: 0,
  },
  totalEarnings: {
    type: Number,
    default: 0,
  },
  // Credit and risk assessment
  creditScore: {
    type: Number,
    default: 0,
    min: 0,
    max: 850,
  },
  riskProfile: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'low',
  },
  // User preferences
  preferredCurrency: {
    type: String,
    default: 'NGN',
  },
  timezone: {
    type: String,
    default: 'Africa/Lagos',
  },
  // Activity tracking
  lastActiveDate: {
    type: Date,
    default: Date.now,
  },
  // Referral system
  referralCode: {
    type: String,
    unique: true,
    sparse: true, // Allows multiple null values
  },
  referredBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  totalReferrals: {
    type: Number,
    default: 0,
  },
  referralEarnings: {
    type: Number,
    default: 0,
  },
  // KYC fields
  kycStatus: {
    type: String,
    enum: ['NOT_SUBMITTED', 'PENDING', 'APPROVED', 'REJECTED'],
    default: 'NOT_SUBMITTED',
  },
  kycType: {
    type: String,
    enum: ['BVN', 'NIN', null],
    default: null,
  },
  kycResult: {
    type: Object,
    default: null,
  }
}, { timestamps: true });

// Generate referral code and hash password before saving
userSchema.pre("save", async function (next) {
  // Generate referral code for new users
  if (this.isNew && !this.referralCode) {
    const crypto = require('crypto');
    this.referralCode = crypto.randomBytes(4).toString('hex').toUpperCase();
  }

  // Hash password if modified
  if (!this.isModified("password")) return next();
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

// Method to compare passwords
userSchema.methods.comparePassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

const User = mongoose.model("User", userSchema);

module.exports = User;
