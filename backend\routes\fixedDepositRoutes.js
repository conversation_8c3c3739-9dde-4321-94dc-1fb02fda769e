const express = require('express');
const router = express.Router();
const FixedDeposit = require('../models/fixedDeposit');
const User = require('../models/user');
const Transaction = require('../models/transaction');
const { authenticateToken } = require('../middleware/authMiddleware');

// POST /api/fixed-deposit/create - Create a new fixed deposit
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { amount, duration, autoRenewal } = req.body;

    // Validation
    if (!amount || !duration) {
      return res.status(400).json({ 
        success: false, 
        message: 'Amount and duration are required' 
      });
    }

    if (amount < 10000) {
      return res.status(400).json({ 
        success: false, 
        message: 'Minimum fixed deposit amount is ₦10,000' 
      });
    }

    if (duration < 30 || duration > 365) {
      return res.status(400).json({ 
        success: false, 
        message: 'Duration must be between 30 and 365 days' 
      });
    }

    // Check user balance
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    if (user.balance < amount) {
      return res.status(400).json({ 
        success: false, 
        message: 'Insufficient balance' 
      });
    }

    // Calculate interest rate based on duration (longer duration = higher rate)
    let interestRate = 15; // Base rate
    if (duration >= 90) interestRate = 16;
    if (duration >= 180) interestRate = 17;
    if (duration >= 365) interestRate = 18;

    // Create fixed deposit
    const fixedDeposit = new FixedDeposit({
      userId,
      amount,
      duration,
      interestRate,
      autoRenewal: autoRenewal || false,
    });

    await fixedDeposit.save();

    // Deduct amount from user balance
    user.balance -= amount;
    user.savingsBalance += amount;
    await user.save();

    // Create transaction record
    const transaction = new Transaction({
      userId,
      type: 'fixed_deposit',
      amount: -amount,
      description: `Fixed deposit created - ${duration} days at ${interestRate}%`,
      reference: fixedDeposit.reference,
      category: 'savings',
      subcategory: 'fixed_deposit',
      paymentMethod: 'wallet',
      balanceAfter: user.balance,
    });

    await transaction.save();

    res.status(201).json({
      success: true,
      message: 'Fixed deposit created successfully',
      data: {
        fixedDeposit,
        projectedReturns: fixedDeposit.calculateAccruedInterest(),
        maturityAmount: amount + fixedDeposit.calculateAccruedInterest()
      }
    });

  } catch (error) {
    console.error('Error creating fixed deposit:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/fixed-deposit/user - Get user's fixed deposits
router.get('/user', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { status } = req.query;

    let query = { userId };
    if (status) {
      query.status = status;
    }

    const deposits = await FixedDeposit.find(query)
      .sort({ createdAt: -1 })
      .populate('userId', 'firstName lastName email');

    // Calculate current values for each deposit
    const depositsWithCurrentValues = deposits.map(deposit => {
      const currentInterest = deposit.calculateAccruedInterest();
      const currentValue = deposit.amount + currentInterest;
      
      return {
        ...deposit.toObject(),
        currentInterest,
        currentValue,
        daysRemaining: deposit.hasMatured() ? 0 : Math.ceil((deposit.maturityDate - new Date()) / (1000 * 60 * 60 * 24))
      };
    });

    // Calculate summary
    const summary = {
      totalDeposits: deposits.length,
      activeDeposits: deposits.filter(d => d.status === 'active').length,
      maturedDeposits: deposits.filter(d => d.status === 'matured').length,
      totalAmount: deposits.reduce((sum, d) => sum + d.amount, 0),
      totalCurrentValue: depositsWithCurrentValues.reduce((sum, d) => sum + d.currentValue, 0),
      totalInterestEarned: depositsWithCurrentValues.reduce((sum, d) => sum + d.currentInterest, 0)
    };

    res.json({
      success: true,
      data: {
        deposits: depositsWithCurrentValues,
        summary
      }
    });

  } catch (error) {
    console.error('Error fetching fixed deposits:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/fixed-deposit/break/:id - Break fixed deposit early
router.post('/break/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { reason } = req.body;

    const deposit = await FixedDeposit.findOne({ _id: id, userId });
    if (!deposit) {
      return res.status(404).json({ 
        success: false, 
        message: 'Fixed deposit not found' 
      });
    }

    if (deposit.status !== 'active') {
      return res.status(400).json({ 
        success: false, 
        message: 'Fixed deposit is not active' 
      });
    }

    // Calculate penalty and final amount
    const accruedInterest = deposit.calculateAccruedInterest();
    const penalty = deposit.calculatePenalty();
    const finalInterest = accruedInterest - penalty;
    const finalAmount = deposit.amount + finalInterest;

    // Update deposit status
    deposit.status = 'broken';
    deposit.terminatedAt = new Date();
    deposit.terminationReason = reason || 'Early termination by user';
    deposit.accruedInterest = accruedInterest;
    deposit.penaltyAmount = penalty;
    await deposit.save();

    // Update user balance
    const user = await User.findById(userId);
    user.balance += finalAmount;
    user.savingsBalance -= deposit.amount;
    user.totalEarnings += finalInterest;
    await user.save();

    // Create transaction records
    const transactions = [];

    // Principal return
    transactions.push(new Transaction({
      userId,
      type: 'withdrawal',
      amount: deposit.amount,
      description: `Fixed deposit principal return - Early termination`,
      reference: `${deposit.reference}_PRINCIPAL`,
      category: 'savings',
      subcategory: 'fixed_deposit_break',
      paymentMethod: 'internal',
      balanceAfter: user.balance - finalInterest,
    }));

    // Interest credit (after penalty)
    if (finalInterest > 0) {
      transactions.push(new Transaction({
        userId,
        type: 'interest',
        amount: finalInterest,
        description: `Fixed deposit interest (after penalty) - ${penalty.toFixed(2)} penalty applied`,
        reference: `${deposit.reference}_INTEREST`,
        category: 'savings',
        subcategory: 'fixed_deposit_interest',
        paymentMethod: 'internal',
        balanceAfter: user.balance,
      }));
    }

    // Penalty transaction
    if (penalty > 0) {
      transactions.push(new Transaction({
        userId,
        type: 'withdrawal_penalty',
        amount: -penalty,
        description: `Early termination penalty - ${deposit.penaltyRate}% of accrued interest`,
        reference: `${deposit.reference}_PENALTY`,
        category: 'penalty',
        subcategory: 'early_termination',
        paymentMethod: 'internal',
        balanceAfter: user.balance,
      }));
    }

    await Transaction.insertMany(transactions);

    res.json({
      success: true,
      message: 'Fixed deposit terminated successfully',
      data: {
        deposit,
        breakdown: {
          principal: deposit.amount,
          grossInterest: accruedInterest,
          penalty,
          netInterest: finalInterest,
          totalReceived: finalAmount
        }
      }
    });

  } catch (error) {
    console.error('Error breaking fixed deposit:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/fixed-deposit/calculate-interest - Calculate projected returns
router.get('/calculate-interest', authenticateToken, async (req, res) => {
  try {
    const { amount, duration } = req.query;

    if (!amount || !duration) {
      return res.status(400).json({ 
        success: false, 
        message: 'Amount and duration are required' 
      });
    }

    // Calculate interest rate based on duration
    let interestRate = 15;
    if (duration >= 90) interestRate = 16;
    if (duration >= 180) interestRate = 17;
    if (duration >= 365) interestRate = 18;

    // Calculate projected returns
    const dailyRate = interestRate / 365 / 100;
    const projectedInterest = amount * Math.pow(1 + dailyRate, duration) - amount;
    const maturityAmount = parseFloat(amount) + projectedInterest;

    res.json({
      success: true,
      data: {
        principal: parseFloat(amount),
        duration: parseInt(duration),
        interestRate,
        projectedInterest: Math.round(projectedInterest * 100) / 100,
        maturityAmount: Math.round(maturityAmount * 100) / 100,
        dailyInterest: Math.round((projectedInterest / duration) * 100) / 100
      }
    });

  } catch (error) {
    console.error('Error calculating interest:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

module.exports = router;
