const mongoose = require('mongoose');

const AutoSaveSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  cardId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Card',
    required: true,
  },
  // AutoSave configuration
  frequency: {
    type: String,
    enum: ['daily', 'weekly', 'monthly'],
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 100, // Minimum ₦100
  },
  // Scheduling
  nextDebitDate: {
    type: Date,
    required: true,
  },
  lastDebitDate: {
    type: Date,
    default: null,
  },
  // Status and control
  isActive: {
    type: Boolean,
    default: true,
  },
  isPaused: {
    type: Boolean,
    default: false,
  },
  pausedUntil: {
    type: Date,
    default: null,
  },
  // Failure handling
  failureCount: {
    type: Number,
    default: 0,
  },
  maxRetries: {
    type: Number,
    default: 3,
  },
  lastFailureDate: {
    type: Date,
    default: null,
  },
  lastFailureReason: {
    type: String,
    default: null,
  },
  // Success tracking
  totalDebits: {
    type: Number,
    default: 0,
  },
  totalAmount: {
    type: Number,
    default: 0,
  },
  lastSuccessfulDebit: {
    type: Date,
    default: null,
  },
  // Target integration
  targetPlanId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SavingsPlan',
    default: null,
  },
  targetType: {
    type: String,
    enum: ['savings_plan', 'flex_savings', 'fixed_deposit', 'safelock'],
    default: 'flex_savings',
  },
  // Smart scheduling preferences
  preferredTime: {
    type: String,
    default: '09:00', // 9 AM
  },
  avoidWeekends: {
    type: Boolean,
    default: true,
  },
  paydayOptimization: {
    type: Boolean,
    default: true,
  },
  // Notification preferences
  notifyBeforeDebit: {
    type: Boolean,
    default: true,
  },
  notifyOnSuccess: {
    type: Boolean,
    default: true,
  },
  notifyOnFailure: {
    type: Boolean,
    default: true,
  },
  // Reference for tracking
  reference: {
    type: String,
    unique: true,
    required: true,
  },
}, { timestamps: true });

// Indexes for efficient queries
AutoSaveSchema.index({ userId: 1 });
AutoSaveSchema.index({ nextDebitDate: 1, isActive: 1 });
AutoSaveSchema.index({ cardId: 1 });

// Pre-save middleware to generate reference and calculate next debit date
AutoSaveSchema.pre('save', function(next) {
  if (this.isNew) {
    // Generate unique reference
    if (!this.reference) {
      const crypto = require('crypto');
      this.reference = 'AS' + crypto.randomBytes(6).toString('hex').toUpperCase();
    }
    
    // Calculate initial next debit date if not set
    if (!this.nextDebitDate) {
      this.nextDebitDate = this.calculateNextDebitDate();
    }
  }
  next();
});

// Method to calculate next debit date based on frequency
AutoSaveSchema.methods.calculateNextDebitDate = function(fromDate = new Date()) {
  const nextDate = new Date(fromDate);
  
  switch (this.frequency) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + 1);
      break;
    case 'weekly':
      nextDate.setDate(nextDate.getDate() + 7);
      break;
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
  }
  
  // Apply smart scheduling
  return this.applySmartScheduling(nextDate);
};

// Method to apply smart scheduling rules
AutoSaveSchema.methods.applySmartScheduling = function(date) {
  const scheduledDate = new Date(date);
  
  // Set preferred time
  const [hours, minutes] = this.preferredTime.split(':');
  scheduledDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);
  
  // Avoid weekends if enabled
  if (this.avoidWeekends) {
    const dayOfWeek = scheduledDate.getDay();
    if (dayOfWeek === 0) { // Sunday
      scheduledDate.setDate(scheduledDate.getDate() + 1); // Move to Monday
    } else if (dayOfWeek === 6) { // Saturday
      scheduledDate.setDate(scheduledDate.getDate() + 2); // Move to Monday
    }
  }
  
  return scheduledDate;
};

// Method to process debit attempt
AutoSaveSchema.methods.processDebit = async function() {
  try {
    // Check if AutoSave is active and not paused
    if (!this.isActive || this.isPaused) {
      return { success: false, reason: 'AutoSave is inactive or paused' };
    }
    
    // Check if it's time for debit
    if (new Date() < this.nextDebitDate) {
      return { success: false, reason: 'Not yet time for debit' };
    }
    
    // Get card and user
    const Card = mongoose.model('Card');
    const User = mongoose.model('User');
    
    const card = await Card.findById(this.cardId);
    const user = await User.findById(this.userId);
    
    if (!card || !card.canBeUsed()) {
      throw new Error('Card is not available or cannot be used');
    }
    
    if (!user) {
      throw new Error('User not found');
    }
    
    // Here you would integrate with Paystack to charge the card
    // For now, we'll simulate the charge
    const chargeResult = await this.chargeCard(card, user);
    
    if (chargeResult.success) {
      // Record successful debit
      this.totalDebits += 1;
      this.totalAmount += this.amount;
      this.lastSuccessfulDebit = new Date();
      this.lastDebitDate = new Date();
      this.failureCount = 0; // Reset failure count
      this.nextDebitDate = this.calculateNextDebitDate();
      
      await this.save();
      
      return { success: true, data: chargeResult };
    } else {
      throw new Error(chargeResult.error);
    }
    
  } catch (error) {
    // Record failure
    this.failureCount += 1;
    this.lastFailureDate = new Date();
    this.lastFailureReason = error.message;
    
    // Calculate retry date with exponential backoff
    const retryHours = Math.pow(2, this.failureCount - 1) * 24; // 1 day, 2 days, 4 days
    this.nextDebitDate = new Date(Date.now() + (retryHours * 60 * 60 * 1000));
    
    // Pause AutoSave if max retries exceeded
    if (this.failureCount >= this.maxRetries) {
      this.isPaused = true;
      this.pausedUntil = new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)); // Pause for 1 week
    }
    
    await this.save();
    
    return { success: false, reason: error.message, failureCount: this.failureCount };
  }
};

// Method to charge card (placeholder for Paystack integration)
AutoSaveSchema.methods.chargeCard = async function(card, user) {
  // This would integrate with Paystack API
  // For now, return a mock success
  return {
    success: true,
    reference: `AS_${Date.now()}`,
    amount: this.amount
  };
};

// Method to pause AutoSave
AutoSaveSchema.methods.pause = function(duration = 7) {
  this.isPaused = true;
  this.pausedUntil = new Date(Date.now() + (duration * 24 * 60 * 60 * 1000));
  return this.save();
};

// Method to resume AutoSave
AutoSaveSchema.methods.resume = function() {
  this.isPaused = false;
  this.pausedUntil = null;
  this.failureCount = 0;
  this.nextDebitDate = this.calculateNextDebitDate();
  return this.save();
};

// Method to update amount
AutoSaveSchema.methods.updateAmount = function(newAmount) {
  if (newAmount < 100) {
    throw new Error('Minimum AutoSave amount is ₦100');
  }
  this.amount = newAmount;
  return this.save();
};

// Method to update frequency
AutoSaveSchema.methods.updateFrequency = function(newFrequency) {
  this.frequency = newFrequency;
  this.nextDebitDate = this.calculateNextDebitDate();
  return this.save();
};

// Static method to get AutoSaves due for processing
AutoSaveSchema.statics.getAutoSavesForProcessing = function() {
  return this.find({
    isActive: true,
    isPaused: false,
    nextDebitDate: { $lte: new Date() }
  }).populate('cardId userId');
};

// Static method to get user's AutoSaves
AutoSaveSchema.statics.getUserAutoSaves = function(userId) {
  return this.find({ userId })
    .populate('cardId', 'maskedPan brand bank')
    .populate('targetPlanId', 'title targetAmount')
    .sort({ createdAt: -1 });
};

module.exports = mongoose.model('AutoSave', AutoSaveSchema);
