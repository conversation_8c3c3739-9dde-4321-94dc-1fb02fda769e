const mongoose = require('mongoose');

const FixedDepositSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 10000, // Minimum ₦10,000
  },
  duration: {
    type: Number,
    required: true,
    min: 30, // Minimum 30 days
    max: 365, // Maximum 365 days
  },
  interestRate: {
    type: Number,
    required: true,
    default: 15, // 15% annual interest rate
  },
  startDate: {
    type: Date,
    required: true,
    default: Date.now,
  },
  maturityDate: {
    type: Date,
    required: true,
  },
  accruedInterest: {
    type: Number,
    default: 0,
  },
  status: {
    type: String,
    enum: ['active', 'matured', 'broken'],
    default: 'active',
  },
  penaltyRate: {
    type: Number,
    default: 25, // 25% penalty on accrued interest for early termination
  },
  autoRenewal: {
    type: Boolean,
    default: false,
  },
  compoundingFrequency: {
    type: String,
    enum: ['daily', 'monthly', 'quarterly'],
    default: 'daily',
  },
  // Track daily interest calculations
  dailyInterestLog: [{
    date: {
      type: Date,
      required: true,
    },
    interestEarned: {
      type: Number,
      required: true,
    },
    cumulativeInterest: {
      type: Number,
      required: true,
    }
  }],
  // Maturity handling
  maturityProcessed: {
    type: Boolean,
    default: false,
  },
  maturityProcessedAt: {
    type: Date,
    default: null,
  },
  // Early termination details
  terminatedAt: {
    type: Date,
    default: null,
  },
  terminationReason: {
    type: String,
    default: null,
  },
  penaltyAmount: {
    type: Number,
    default: 0,
  },
  // Reference for tracking
  reference: {
    type: String,
    unique: true,
    required: true,
  },
}, { timestamps: true });

// Pre-save middleware to calculate maturity date and generate reference
FixedDepositSchema.pre('save', function(next) {
  if (this.isNew) {
    // Calculate maturity date
    this.maturityDate = new Date(this.startDate.getTime() + (this.duration * 24 * 60 * 60 * 1000));
    
    // Generate unique reference
    if (!this.reference) {
      const crypto = require('crypto');
      this.reference = 'FD' + crypto.randomBytes(6).toString('hex').toUpperCase();
    }
  }
  next();
});

// Method to calculate current accrued interest
FixedDepositSchema.methods.calculateAccruedInterest = function() {
  const now = new Date();
  const startDate = this.startDate;
  const daysElapsed = Math.floor((now - startDate) / (1000 * 60 * 60 * 24));
  
  if (daysElapsed <= 0) return 0;
  
  // Daily compound interest calculation
  const dailyRate = this.interestRate / 365 / 100;
  const accruedInterest = this.amount * Math.pow(1 + dailyRate, daysElapsed) - this.amount;
  
  return Math.round(accruedInterest * 100) / 100; // Round to 2 decimal places
};

// Method to calculate penalty for early termination
FixedDepositSchema.methods.calculatePenalty = function() {
  const accruedInterest = this.calculateAccruedInterest();
  return Math.round(accruedInterest * (this.penaltyRate / 100) * 100) / 100;
};

// Method to check if deposit has matured
FixedDepositSchema.methods.hasMatured = function() {
  return new Date() >= this.maturityDate;
};

// Method to process maturity
FixedDepositSchema.methods.processMaturity = async function() {
  if (this.hasMatured() && !this.maturityProcessed) {
    this.accruedInterest = this.calculateAccruedInterest();
    this.status = 'matured';
    this.maturityProcessed = true;
    this.maturityProcessedAt = new Date();
    
    // If auto-renewal is enabled, create new deposit
    if (this.autoRenewal) {
      const newAmount = this.amount + this.accruedInterest;
      const FixedDeposit = mongoose.model('FixedDeposit');
      
      const newDeposit = new FixedDeposit({
        userId: this.userId,
        amount: newAmount,
        duration: this.duration,
        interestRate: this.interestRate,
        autoRenewal: this.autoRenewal,
        compoundingFrequency: this.compoundingFrequency,
      });
      
      await newDeposit.save();
    }
    
    await this.save();
    return true;
  }
  return false;
};

// Static method to get user's active deposits
FixedDepositSchema.statics.getUserActiveDeposits = function(userId) {
  return this.find({ userId, status: 'active' }).sort({ createdAt: -1 });
};

// Static method to get deposits due for maturity processing
FixedDepositSchema.statics.getDepositsForMaturityProcessing = function() {
  return this.find({
    status: 'active',
    maturityDate: { $lte: new Date() },
    maturityProcessed: false
  });
};

module.exports = mongoose.model('FixedDeposit', FixedDepositSchema);
