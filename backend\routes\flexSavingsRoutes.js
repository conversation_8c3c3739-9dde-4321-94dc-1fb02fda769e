const express = require('express');
const router = express.Router();
const FlexSavings = require('../models/flexSavings');
const User = require('../models/user');
const Transaction = require('../models/transaction');
const { authenticateToken } = require('../middleware/authMiddleware');

// GET /api/flex-savings/account - Get or create user's flex savings account
router.get('/account', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const flexAccount = await FlexSavings.getOrCreateForUser(userId);
    
    // Calculate projected daily interest
    const projectedDailyInterest = flexAccount.calculateDailyInterest();
    
    // Get recent transactions
    const recentTransactions = await Transaction.find({
      userId,
      $or: [
        { type: 'flex_savings' },
        { subcategory: 'flex_savings' }
      ]
    })
    .sort({ createdAt: -1 })
    .limit(10);

    res.json({
      success: true,
      data: {
        account: flexAccount,
        projectedDailyInterest,
        annualInterestRate: flexAccount.annualInterestRate,
        recentTransactions
      }
    });

  } catch (error) {
    console.error('Error fetching flex savings account:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/flex-savings/deposit - Deposit money to flex savings
router.post('/deposit', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid deposit amount' 
      });
    }

    // Check user balance
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    if (user.balance < amount) {
      return res.status(400).json({ 
        success: false, 
        message: 'Insufficient wallet balance' 
      });
    }

    // Get or create flex account
    const flexAccount = await FlexSavings.getOrCreateForUser(userId);
    
    // Process deposit
    const previousBalance = flexAccount.balance;
    flexAccount.deposit(amount);
    await flexAccount.save();

    // Update user balances
    user.balance -= amount;
    user.savingsBalance += amount;
    await user.save();

    // Create transaction record
    const transaction = new Transaction({
      userId,
      type: 'deposit',
      amount,
      description: 'Deposit to Flex Savings',
      reference: `FLEX_DEP_${Date.now()}`,
      category: 'savings',
      subcategory: 'flex_savings',
      paymentMethod: 'wallet',
      balanceAfter: user.balance,
    });

    await transaction.save();

    res.json({
      success: true,
      message: 'Deposit successful',
      data: {
        previousBalance,
        newBalance: flexAccount.balance,
        depositAmount: amount,
        projectedDailyInterest: flexAccount.calculateDailyInterest()
      }
    });

  } catch (error) {
    console.error('Error depositing to flex savings:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/flex-savings/withdraw - Withdraw money from flex savings
router.post('/withdraw', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid withdrawal amount' 
      });
    }

    // Get flex account
    const flexAccount = await FlexSavings.findOne({ userId });
    if (!flexAccount) {
      return res.status(404).json({ 
        success: false, 
        message: 'Flex savings account not found' 
      });
    }

    if (flexAccount.balance < amount) {
      return res.status(400).json({ 
        success: false, 
        message: 'Insufficient flex savings balance' 
      });
    }

    // Process withdrawal
    const previousBalance = flexAccount.balance;
    flexAccount.withdraw(amount);
    await flexAccount.save();

    // Update user balances
    const user = await User.findById(userId);
    user.balance += amount;
    user.savingsBalance -= amount;
    await user.save();

    // Create transaction record
    const transaction = new Transaction({
      userId,
      type: 'withdrawal',
      amount,
      description: 'Withdrawal from Flex Savings',
      reference: `FLEX_WTH_${Date.now()}`,
      category: 'savings',
      subcategory: 'flex_savings',
      paymentMethod: 'wallet',
      balanceAfter: user.balance,
    });

    await transaction.save();

    res.json({
      success: true,
      message: 'Withdrawal successful',
      data: {
        previousBalance,
        newBalance: flexAccount.balance,
        withdrawalAmount: amount,
        projectedDailyInterest: flexAccount.calculateDailyInterest()
      }
    });

  } catch (error) {
    console.error('Error withdrawing from flex savings:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/flex-savings/interest-history - Get interest earning history
router.get('/interest-history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = 'month' } = req.query; // month, quarter, year

    const flexAccount = await FlexSavings.findOne({ userId });
    if (!flexAccount) {
      return res.status(404).json({ 
        success: false, 
        message: 'Flex savings account not found' 
      });
    }

    let history = [];
    const now = new Date();

    if (period === 'month') {
      // Get last 30 days of daily balances
      const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
      history = flexAccount.dailyBalances
        .filter(record => record.date >= thirtyDaysAgo)
        .sort((a, b) => b.date - a.date);
    } else if (period === 'quarter') {
      // Get last 3 months of monthly summaries
      history = flexAccount.monthlyInterest
        .slice(-3)
        .sort((a, b) => b.month.localeCompare(a.month));
    } else if (period === 'year') {
      // Get last 12 months of monthly summaries
      history = flexAccount.monthlyInterest
        .slice(-12)
        .sort((a, b) => b.month.localeCompare(a.month));
    }

    // Calculate summary statistics
    const totalInterestInPeriod = period === 'month' 
      ? history.reduce((sum, record) => sum + record.interestEarned, 0)
      : history.reduce((sum, record) => sum + record.totalInterest, 0);

    const averageBalance = period === 'month'
      ? history.length > 0 ? history.reduce((sum, record) => sum + record.balance, 0) / history.length : 0
      : history.length > 0 ? history.reduce((sum, record) => sum + record.averageBalance, 0) / history.length : 0;

    res.json({
      success: true,
      data: {
        period,
        history,
        summary: {
          totalInterestEarned: totalInterestInPeriod,
          averageBalance,
          currentBalance: flexAccount.balance,
          totalLifetimeInterest: flexAccount.totalInterestEarned,
          annualInterestRate: flexAccount.annualInterestRate
        }
      }
    });

  } catch (error) {
    console.error('Error fetching interest history:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/flex-savings/calculate-interest - Calculate projected interest
router.get('/calculate-interest', authenticateToken, async (req, res) => {
  try {
    const { amount, days = 30 } = req.query;

    if (!amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid amount' 
      });
    }

    const dailyRate = 0.0411 / 100; // 15% annual = 0.0411% daily
    const projectedInterest = parseFloat(amount) * dailyRate * parseInt(days);
    const finalAmount = parseFloat(amount) + projectedInterest;

    res.json({
      success: true,
      data: {
        principal: parseFloat(amount),
        days: parseInt(days),
        dailyInterestRate: 0.0411,
        annualInterestRate: 15,
        projectedInterest: Math.round(projectedInterest * 100) / 100,
        finalAmount: Math.round(finalAmount * 100) / 100,
        dailyInterest: Math.round((projectedInterest / days) * 100) / 100
      }
    });

  } catch (error) {
    console.error('Error calculating interest:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

module.exports = router;
