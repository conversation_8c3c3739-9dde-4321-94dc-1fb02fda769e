const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/user');

async function resetAdminPassword() {
  try {
    console.log('🔐 Resetting admin password...');
    
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/kojapay_savings';
    console.log(`📡 Connecting to MongoDB: ${mongoUri}`);
    
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB successfully');

    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'KojaPay2024Admin!';

    // Find the admin user
    const adminUser = await User.findOne({ email: adminEmail });
    
    if (!adminUser) {
      console.log('❌ Admin user not found');
      process.exit(1);
    }

    console.log(`👤 Found admin user: ${adminUser.firstName} ${adminUser.lastName}`);
    console.log(`📧 Email: ${adminUser.email}`);
    console.log(`🔑 Current role: ${adminUser.role}`);

    // Set the password directly (the pre-save hook will hash it)
    adminUser.password = adminPassword;
    await adminUser.save();

    console.log('✅ Admin password updated successfully!');
    console.log('\n🔐 Admin Credentials:');
    console.log(`Email: ${adminEmail}`);
    console.log(`Password: ${adminPassword}`);
    
    // Test the password
    const isPasswordValid = await bcrypt.compare(adminPassword, adminUser.password);
    console.log(`🧪 Password test: ${isPasswordValid ? '✅ Valid' : '❌ Invalid'}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Password reset failed:', error);
    process.exit(1);
  }
}

// Run the reset if this file is executed directly
if (require.main === module) {
  resetAdminPassword();
}

module.exports = { resetAdminPassword };
