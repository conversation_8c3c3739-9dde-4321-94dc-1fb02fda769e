
import { API_CONFIG } from '@/config/env';
import axios from 'axios';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

// Types
export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string | null;
  avatar_url: string | null;
  status: string | null;
  kyc_status: string | null;
  created_at: string;
  isAdmin?: boolean;
  roles?: any[];
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  phone?: string;
  status?: 'active' | 'suspended' | 'blocked';
  isAdmin?: boolean;
}

const API_URL = API_CONFIG.BASE_URL;

console.log('👥 Users API Configuration:', {
  API_URL,
  BACKEND_URL: API_CONFIG.BACKEND_URL
});

export function useUsersApi() {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Helper to get token from localStorage
  const getToken = () => localStorage.getItem('token');

  // Get all users
  const getAllUsers = useCallback(async () => {
    setIsLoading(true);

    console.log('👥 Fetching all users:', {
      url: `${API_URL}/auth/users`
    });

    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.get(`${API_URL}/auth/users`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('✅ Users fetched successfully:', {
        count: response.data?.length || 0,
        data: response.data
      });

      setUsers(response.data);
      return { data: response.data, error: null };
    } catch (error) {
      console.error('❌ Error fetching users:', error);
      const message = axios.isAxiosError(error)
        ? error.response?.data?.message || error.message
        : 'Failed to fetch users';

      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get user by ID
  const getUserById = useCallback(async (userId: string) => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.get(`${API_URL}/auth/users/${userId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setSelectedUser(response.data);
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching user:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch user';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update user
  const updateUser = useCallback(async (userId: string, userData: UpdateUserData) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.put(`${API_URL}/auth/users/${userId}`,
        { ...userData },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      // Update local state
      setSelectedUser(response.data);
      setUsers(prev => 
        prev.map(user => 
          user.id === userId ? response.data : user
        )
      );
      
      toast.success('User updated successfully');
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error updating user:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to update user';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, []);

  // Delete user
  const deleteUser = useCallback(async (userId: string) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      await axios.delete(`${API_URL}/auth/users/${userId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      // Update local state
      setUsers(prev => prev.filter(user => user.id !== userId));
      if (selectedUser?.id === userId) {
        setSelectedUser(null);
      }
      
      toast.success('User deleted successfully');
      return { success: true, error: null };
    } catch (error) {
      console.error('Error deleting user:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to delete user';
      
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setIsSaving(false);
    }
  }, [selectedUser]);

  // Get current user profile
  const getProfile = useCallback(async () => {
    setIsLoading(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.get(`${API_URL}/auth/profile`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching profile:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch profile';
      
      return { data: null, error: message };
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    users,
    selectedUser,
    isLoading,
    isSaving,
    getAllUsers,
    getUserById,
    updateUser,
    deleteUser,
    getProfile
  };
}
