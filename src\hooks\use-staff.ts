
import { API_CONFIG } from '@/config/env';
import axios from 'axios';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

// Types
export type StaffRole = {
  id: string;
  name: string;
  description: string;
  permissions: string[];
};

export type StaffMember = {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role_id: string;
  role_name: string;
  permissions: string[];
  last_login: string;
};

const API_URL = API_CONFIG.BASE_URL;

export function useStaff() {
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [isLoadingMembers, setIsLoadingMembers] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [roles, setRoles] = useState<StaffRole[]>([]);
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);

  // Get the JWT token from localStorage
  const getToken = () => localStorage.getItem('token');

  // Get all staff roles with permissions
  const getRoles = useCallback(async () => {
    setIsLoadingRoles(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.get(`${API_URL}/staff/roles`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setRoles(response.data);
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching staff roles:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch staff roles';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoadingRoles(false);
    }
  }, []);

  // Get all staff members
  const getStaffMembers = useCallback(async () => {
    setIsLoadingMembers(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.get(`${API_URL}/staff/members`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setStaffMembers(response.data);
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error fetching staff members:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to fetch staff members';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsLoadingMembers(false);
    }
  }, []);

  // Create a new staff role
  const createRole = useCallback(async (name: string, description: string) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/staff/roles`, 
        { name, description },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('Role created successfully');
      // Refresh roles
      await getRoles();
      
      return { data: response.data.role, error: null };
    } catch (error) {
      console.error('Error creating staff role:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to create staff role';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, [getRoles]);

  // Update role permissions
  const updateRolePermissions = useCallback(async (roleId: string, permissions: string[]) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/staff/update-permissions`, 
        { roleId, permissions },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('Role permissions updated successfully');
      // Refresh roles
      await getRoles();
      
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error updating role permissions:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to update role permissions';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, [getRoles]);

  // Delete a staff role
  const deleteRole = useCallback(async (roleId: string) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      await axios.delete(`${API_URL}/staff/roles/${roleId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      toast.success('Role deleted successfully');
      // Refresh roles
      await getRoles();
      
      return { success: true, error: null };
    } catch (error) {
      console.error('Error deleting staff role:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to delete staff role';
      
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setIsSaving(false);
    }
  }, [getRoles]);

  // Assign role to user
  const assignRoleToUser = useCallback(async (userId: string, roleId: string) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      const response = await axios.post(`${API_URL}/staff/assign-role`, 
        { userId, roleId },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      toast.success('Role assigned successfully');
      // Refresh staff members
      await getStaffMembers();
      
      return { data: response.data, error: null };
    } catch (error) {
      console.error('Error assigning role to user:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to assign role to user';
      
      toast.error(message);
      return { data: null, error: message };
    } finally {
      setIsSaving(false);
    }
  }, [getStaffMembers]);

  // Remove role from user
  const removeRoleFromUser = useCallback(async (userId: string, roleId: string) => {
    setIsSaving(true);
    try {
      const token = getToken();
      if (!token) throw new Error('Authentication required');

      await axios.delete(`${API_URL}/staff/assignment/${userId}/${roleId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      toast.success('Role removed successfully from user');
      // Refresh staff members
      await getStaffMembers();
      
      return { success: true, error: null };
    } catch (error) {
      console.error('Error removing role from user:', error);
      const message = axios.isAxiosError(error) 
        ? error.response?.data?.message || error.message 
        : 'Failed to remove role from user';
      
      toast.error(message);
      return { success: false, error: message };
    } finally {
      setIsSaving(false);
    }
  }, [getStaffMembers]);

  return {
    roles,
    staffMembers,
    isLoadingRoles,
    isLoadingMembers,
    isSaving,
    getRoles,
    getStaffMembers,
    createRole,
    updateRolePermissions,
    deleteRole,
    assignRoleToUser,
    removeRoleFromUser
  };
}
