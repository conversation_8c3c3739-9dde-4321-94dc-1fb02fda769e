// Form validation utilities for KojaPay Savings App

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  min?: number;
  max?: number;
  custom?: (value: any) => string | null;
}

export interface ValidationRules {
  [key: string]: ValidationRule;
}

export interface ValidationErrors {
  [key: string]: string;
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[1-9]\d{1,14}$/,
  nigerianPhone: /^(\+234|234|0)?[789][01]\d{8}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  nin: /^\d{11}$/,
  bvn: /^\d{11}$/,
  amount: /^\d+(\.\d{1,2})?$/,
  referralCode: /^[A-Z0-9]{6,10}$/,
};

// Common validation rules
export const COMMON_RULES: ValidationRules = {
  firstName: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z\s]+$/,
  },
  lastName: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z\s]+$/,
  },
  email: {
    required: true,
    pattern: VALIDATION_PATTERNS.email,
    maxLength: 100,
  },
  phone: {
    required: false,
    pattern: VALIDATION_PATTERNS.nigerianPhone,
  },
  password: {
    required: true,
    minLength: 8,
    maxLength: 128,
    pattern: VALIDATION_PATTERNS.password,
  },
  confirmPassword: {
    required: true,
    custom: (value: string, formData: any) => {
      if (value !== formData.password) {
        return 'Passwords do not match';
      }
      return null;
    },
  },
  amount: {
    required: true,
    pattern: VALIDATION_PATTERNS.amount,
    min: 0.01,
  },
  nin: {
    required: false,
    pattern: VALIDATION_PATTERNS.nin,
  },
  bvn: {
    required: false,
    pattern: VALIDATION_PATTERNS.bvn,
  },
  referralCode: {
    required: false,
    pattern: VALIDATION_PATTERNS.referralCode,
  },
};

// Validation function
export function validateForm(formData: any, rules: ValidationRules): ValidationErrors {
  const errors: ValidationErrors = {};

  Object.keys(rules).forEach((field) => {
    const rule = rules[field];
    const value = formData[field];

    // Check required
    if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      errors[field] = `${formatFieldName(field)} is required`;
      return;
    }

    // Skip other validations if field is empty and not required
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return;
    }

    // Check minLength
    if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
      errors[field] = `${formatFieldName(field)} must be at least ${rule.minLength} characters`;
      return;
    }

    // Check maxLength
    if (rule.maxLength && typeof value === 'string' && value.length > rule.maxLength) {
      errors[field] = `${formatFieldName(field)} must not exceed ${rule.maxLength} characters`;
      return;
    }

    // Check pattern
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      errors[field] = getPatternErrorMessage(field, rule.pattern);
      return;
    }

    // Check min value
    if (rule.min !== undefined && typeof value === 'number' && value < rule.min) {
      errors[field] = `${formatFieldName(field)} must be at least ${rule.min}`;
      return;
    }

    // Check max value
    if (rule.max !== undefined && typeof value === 'number' && value > rule.max) {
      errors[field] = `${formatFieldName(field)} must not exceed ${rule.max}`;
      return;
    }

    // Check custom validation
    if (rule.custom) {
      const customError = rule.custom(value, formData);
      if (customError) {
        errors[field] = customError;
        return;
      }
    }
  });

  return errors;
}

// Format field name for error messages
function formatFieldName(field: string): string {
  return field
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
}

// Get pattern-specific error messages
function getPatternErrorMessage(field: string, pattern: RegExp): string {
  const fieldName = formatFieldName(field);

  if (pattern === VALIDATION_PATTERNS.email) {
    return 'Please enter a valid email address';
  }
  if (pattern === VALIDATION_PATTERNS.phone || pattern === VALIDATION_PATTERNS.nigerianPhone) {
    return 'Please enter a valid Nigerian phone number';
  }
  if (pattern === VALIDATION_PATTERNS.password) {
    return 'Password must contain at least 8 characters with uppercase, lowercase, number, and special character';
  }
  if (pattern === VALIDATION_PATTERNS.nin) {
    return 'NIN must be exactly 11 digits';
  }
  if (pattern === VALIDATION_PATTERNS.bvn) {
    return 'BVN must be exactly 11 digits';
  }
  if (pattern === VALIDATION_PATTERNS.amount) {
    return 'Please enter a valid amount';
  }
  if (pattern === VALIDATION_PATTERNS.referralCode) {
    return 'Referral code must be 6-10 alphanumeric characters';
  }

  return `${fieldName} format is invalid`;
}

// Specific validation functions
export const validateEmail = (email: string): string | null => {
  if (!email) return 'Email is required';
  if (!VALIDATION_PATTERNS.email.test(email)) return 'Please enter a valid email address';
  return null;
};

export const validatePassword = (password: string): string | null => {
  if (!password) return 'Password is required';
  if (password.length < 8) return 'Password must be at least 8 characters';
  if (!VALIDATION_PATTERNS.password.test(password)) {
    return 'Password must contain uppercase, lowercase, number, and special character';
  }
  return null;
};

export const validatePhone = (phone: string): string | null => {
  if (!phone) return null; // Phone is optional in most cases
  if (!VALIDATION_PATTERNS.nigerianPhone.test(phone)) {
    return 'Please enter a valid Nigerian phone number';
  }
  return null;
};

export const validateAmount = (amount: string | number, min = 0.01, max?: number): string | null => {
  if (!amount) return 'Amount is required';
  
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) return 'Please enter a valid amount';
  if (numAmount < min) return `Amount must be at least ₦${min.toLocaleString()}`;
  if (max && numAmount > max) return `Amount must not exceed ₦${max.toLocaleString()}`;
  
  return null;
};

export const validateNIN = (nin: string): string | null => {
  if (!nin) return null; // NIN is optional in most cases
  if (!VALIDATION_PATTERNS.nin.test(nin)) return 'NIN must be exactly 11 digits';
  return null;
};

export const validateBVN = (bvn: string): string | null => {
  if (!bvn) return null; // BVN is optional in most cases
  if (!VALIDATION_PATTERNS.bvn.test(bvn)) return 'BVN must be exactly 11 digits';
  return null;
};

export const validateReferralCode = (code: string): string | null => {
  if (!code) return null; // Referral code is optional
  if (!VALIDATION_PATTERNS.referralCode.test(code)) {
    return 'Referral code must be 6-10 alphanumeric characters';
  }
  return null;
};

// Savings-specific validations
export const validateFixedDepositAmount = (amount: string | number): string | null => {
  return validateAmount(amount, 10000, 50000000); // ₦10K to ₦50M
};

export const validateSafeLockAmount = (amount: string | number): string | null => {
  return validateAmount(amount, 5000, 50000000); // ₦5K to ₦50M
};

export const validateFlexSavingsAmount = (amount: string | number): string | null => {
  return validateAmount(amount, 100, 50000000); // ₦100 to ₦50M
};

export const validateAutoSaveAmount = (amount: string | number): string | null => {
  return validateAmount(amount, 100, 1000000); // ₦100 to ₦1M
};

export const validateDuration = (duration: string | number, min = 1, max = 365): string | null => {
  if (!duration) return 'Duration is required';
  
  const numDuration = typeof duration === 'string' ? parseInt(duration) : duration;
  
  if (isNaN(numDuration)) return 'Please enter a valid duration';
  if (numDuration < min) return `Duration must be at least ${min} day${min > 1 ? 's' : ''}`;
  if (numDuration > max) return `Duration must not exceed ${max} days`;
  
  return null;
};

// File validation
export const validateFile = (file: File, maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']): string | null => {
  if (!file) return 'File is required';
  
  if (file.size > maxSize) {
    return `File size must not exceed ${(maxSize / (1024 * 1024)).toFixed(1)}MB`;
  }
  
  if (!allowedTypes.includes(file.type)) {
    return `File type must be one of: ${allowedTypes.map(type => type.split('/')[1]).join(', ')}`;
  }
  
  return null;
};

export default {
  validateForm,
  validateEmail,
  validatePassword,
  validatePhone,
  validateAmount,
  validateNIN,
  validateBVN,
  validateReferralCode,
  validateFixedDepositAmount,
  validateSafeLockAmount,
  validateFlexSavingsAmount,
  validateAutoSaveAmount,
  validateDuration,
  validateFile,
  VALIDATION_PATTERNS,
  COMMON_RULES,
};
