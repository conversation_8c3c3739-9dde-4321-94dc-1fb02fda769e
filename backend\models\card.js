const mongoose = require('mongoose');

const CardSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  // Paystack authorization details
  authorizationCode: {
    type: String,
    required: true,
    unique: true,
  },
  cardToken: {
    type: String,
    required: true, // Paystack card token
  },
  // Card details (masked for security)
  last4: {
    type: String,
    required: true,
  },
  first6: {
    type: String,
    required: true,
  },
  brand: {
    type: String,
    required: true, // visa, mastercard, verve, etc.
  },
  cardType: {
    type: String,
    required: true, // debit, credit
  },
  bank: {
    type: String,
    required: true,
  },
  countryCode: {
    type: String,
    default: 'NG',
  },
  // Expiry information
  expiryMonth: {
    type: String,
    required: true,
  },
  expiryYear: {
    type: String,
    required: true,
  },
  // Card status and settings
  isDefault: {
    type: Boolean,
    default: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  // Verification details
  verificationAmount: {
    type: Number,
    default: 100, // ₦100 verification charge
  },
  verificationReference: {
    type: String,
    default: null,
  },
  verifiedAt: {
    type: Date,
    default: null,
  },
  // Usage tracking
  lastUsed: {
    type: Date,
    default: null,
  },
  totalTransactions: {
    type: Number,
    default: 0,
  },
  totalAmount: {
    type: Number,
    default: 0,
  },
  // Failure tracking for fraud detection
  failedTransactions: {
    type: Number,
    default: 0,
  },
  lastFailure: {
    type: Date,
    default: null,
  },
  isBlocked: {
    type: Boolean,
    default: false,
  },
  blockReason: {
    type: String,
    default: null,
  },
  // Metadata from Paystack
  signature: {
    type: String,
    default: null,
  },
  reusable: {
    type: Boolean,
    default: true,
  },
  // Card nickname for user identification
  nickname: {
    type: String,
    default: null,
  },
}, { timestamps: true });

// Indexes for efficient queries
CardSchema.index({ userId: 1 });
CardSchema.index({ authorizationCode: 1 });
CardSchema.index({ isDefault: 1, userId: 1 });

// Virtual for masked card number display
CardSchema.virtual('maskedPan').get(function() {
  return `${this.first6}****${this.last4}`;
});

// Virtual for expiry display
CardSchema.virtual('expiryDisplay').get(function() {
  return `${this.expiryMonth}/${this.expiryYear}`;
});

// Virtual to check if card is expired
CardSchema.virtual('isExpired').get(function() {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  
  const cardYear = parseInt(this.expiryYear);
  const cardMonth = parseInt(this.expiryMonth);
  
  return cardYear < currentYear || (cardYear === currentYear && cardMonth < currentMonth);
});

// Method to verify card with small charge
CardSchema.methods.initiateVerification = async function() {
  if (this.isVerified) {
    throw new Error('Card is already verified');
  }
  
  const crypto = require('crypto');
  this.verificationReference = 'VERIFY_' + crypto.randomBytes(8).toString('hex').toUpperCase();
  
  // This would integrate with Paystack to charge the verification amount
  // For now, we'll mark it as pending verification
  await this.save();
  
  return {
    reference: this.verificationReference,
    amount: this.verificationAmount,
    authorizationCode: this.authorizationCode
  };
};

// Method to complete verification
CardSchema.methods.completeVerification = async function() {
  this.isVerified = true;
  this.verifiedAt = new Date();
  await this.save();
  
  return true;
};

// Method to set as default card
CardSchema.methods.setAsDefault = async function() {
  // Remove default status from other cards
  await mongoose.model('Card').updateMany(
    { userId: this.userId, _id: { $ne: this._id } },
    { isDefault: false }
  );
  
  this.isDefault = true;
  await this.save();
  
  return true;
};

// Method to record successful transaction
CardSchema.methods.recordTransaction = function(amount) {
  this.lastUsed = new Date();
  this.totalTransactions += 1;
  this.totalAmount += amount;
  this.failedTransactions = 0; // Reset failure count on success
  
  return this.save();
};

// Method to record failed transaction
CardSchema.methods.recordFailure = function(reason) {
  this.failedTransactions += 1;
  this.lastFailure = new Date();
  
  // Block card after 3 consecutive failures
  if (this.failedTransactions >= 3) {
    this.isBlocked = true;
    this.blockReason = `Blocked due to ${this.failedTransactions} consecutive failures. Last reason: ${reason}`;
  }
  
  return this.save();
};

// Method to unblock card
CardSchema.methods.unblock = function() {
  this.isBlocked = false;
  this.blockReason = null;
  this.failedTransactions = 0;
  
  return this.save();
};

// Method to check if card can be used
CardSchema.methods.canBeUsed = function() {
  return this.isActive && 
         this.isVerified && 
         !this.isBlocked && 
         !this.isExpired && 
         this.reusable;
};

// Static method to get user's default card
CardSchema.statics.getUserDefaultCard = function(userId) {
  return this.findOne({ userId, isDefault: true, isActive: true });
};

// Static method to get user's usable cards
CardSchema.statics.getUserUsableCards = function(userId) {
  return this.find({ 
    userId, 
    isActive: true, 
    isVerified: true, 
    isBlocked: false 
  }).sort({ isDefault: -1, lastUsed: -1 });
};

// Static method to create card from Paystack authorization
CardSchema.statics.createFromPaystackAuth = async function(userId, authData) {
  const existingCard = await this.findOne({ 
    authorizationCode: authData.authorization_code 
  });
  
  if (existingCard) {
    throw new Error('Card already exists');
  }
  
  const card = new this({
    userId,
    authorizationCode: authData.authorization_code,
    cardToken: authData.authorization_code, // Paystack uses auth code as token
    last4: authData.last4,
    first6: authData.bin,
    brand: authData.brand,
    cardType: authData.card_type,
    bank: authData.bank,
    countryCode: authData.country_code,
    expiryMonth: authData.exp_month,
    expiryYear: authData.exp_year,
    signature: authData.signature,
    reusable: authData.reusable,
  });
  
  // Set as default if it's the user's first card
  const userCardCount = await this.countDocuments({ userId });
  if (userCardCount === 0) {
    card.isDefault = true;
  }
  
  await card.save();
  return card;
};

module.exports = mongoose.model('Card', CardSchema);
