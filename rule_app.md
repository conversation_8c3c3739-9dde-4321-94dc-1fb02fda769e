# Backend Requirements & Feature Gap Analysis
## Kojapay Savings App - Complete Backend Specification

---

## CURRENT BACKEND ANALYSIS

Based on your existing backend implementation, here's what you have and what's missing for a world-class savings app:

---

## EXISTING FEATURES (Well Implemented)

### 1. Authentication & User Management
- JWT-based authentication
- User registration/login
- Profile management
- KYC verification system
- Admin user management

### 2. Core Savings Features
- Individual savings plans
- Group savings functionality
- Target savings goals
- Deposit management with Paystack
- Withdrawal system (both in-app and external)

### 3. Payment Integration
- Paystack deposit integration
- Paystack withdrawal/payout system
- Bank account verification
- Transaction recording
- Webhook handling

### 4. Admin Features
- User management
- Withdrawal approval system
- KYC management
- Settings configuration
- Penalty management

---

## CRITICAL MISSING FEATURES WITH IMPLEMENTATION LOGIC

### 1. Fixed Deposit Plans

#### Business Logic:
Fixed deposits work like traditional bank term deposits but with better rates and digital convenience. Users lock money for specific periods (30-365 days) and earn guaranteed returns.

**Implementation Flow:**
1. **Creation Process**: User selects amount (min ₦10,000), duration, and sees projected returns
2. **Fund Locking**: Money is immediately debited from wallet and locked in fixed deposit account
3. **Interest Accrual**: Daily compound interest calculation (e.g., 15% annually = 0.041% daily)
4. **Maturity Handling**: Auto-credit principal + interest to user wallet on maturity date
5. **Early Termination**: Allow breaking with penalty (25% of accrued interest forfeited)
6. **Auto-Renewal**: Optional feature to rollover matured deposits

**Database Schema:**
```javascript
const FixedDepositSchema = {
  userId: ObjectId,
  amount: Number,
  duration: Number, // in days
  interestRate: Number, // annual percentage
  startDate: Date,
  maturityDate: Date,
  accruedInterest: Number,
  status: ['active', 'matured', 'broken'],
  penaltyRate: Number,
  autoRenewal: Boolean,
  compoundingFrequency: 'daily'
}
```

### 2. Flexible Savings (Flex Account)

#### Business Logic:
Flex savings is like a high-yield savings account where users can deposit and withdraw anytime while earning daily interest. It's the most liquid savings option.

**Implementation Flow:**
1. **Instant Deposits**: Users can add money anytime from wallet or card
2. **Daily Interest**: Calculate interest every midnight on the previous day's balance
3. **Instant Withdrawals**: No restrictions, immediate transfer to wallet
4. **Interest Calculation**: Only full days count for interest (deposit at 2pm, interest starts next day)
5. **Compounding**: Interest is added to principal daily for compound growth

**Database Schema:**
```javascript
const FlexSavingsSchema = {
  userId: ObjectId,
  balance: Number,
  dailyInterestRate: Number, // annual rate / 365
  lastInterestCalculation: Date,
  totalInterestEarned: Number,
  isActive: Boolean
}
```

### 3. SafeLock (Time-Locked Savings)

#### Business Logic:
SafeLock is the highest-yield savings option where money is completely inaccessible for the lock period. Higher risk (no liquidity) = higher reward (better interest rates).

**Implementation Flow:**
1. **Lock Creation**: User chooses amount and lock duration (1-12 months)
2. **Complete Lock**: No withdrawal option until maturity (unlike fixed deposits)
3. **Premium Rates**: Higher interest rates (18-25% annually) due to guaranteed lock
4. **Emergency Break**: Optional feature with severe penalty (50% of principal)
5. **Maturity Bonus**: Additional bonus for completing full term

**Database Schema:**
```javascript
const SafeLockSchema = {
  userId: ObjectId,
  amount: Number,
  lockDuration: Number, // in days
  interestRate: Number,
  startDate: Date,
  maturityDate: Date,
  canBreak: Boolean,
  breakagePenalty: Number,
  status: ['active', 'matured', 'broken'],
  maturityBonus: Number
}
```

### 4. AutoSave Feature

#### Business Logic:
AutoSave automates the savings habit by automatically debiting user's card at set intervals. It removes the friction of manual saving and builds consistency.

**Implementation Flow:**
1. **Card Authorization**: User authorizes card for recurring charges via Paystack
2. **Schedule Setup**: Choose frequency (daily, weekly, monthly) and amount
3. **Smart Timing**: Debit at optimal times (avoid weekends, consider payday patterns)
4. **Failure Handling**: If charge fails, retry 3 times over 3 days with exponential backoff
5. **Target Integration**: Auto-saved money goes to specific savings goals
6. **Notifications**: Alert before debit, confirm success, notify failures

**Database Schema:**
```javascript
const AutoSaveSchema = {
  userId: ObjectId,
  cardId: ObjectId, // reference to saved card
  frequency: ['daily', 'weekly', 'monthly'],
  amount: Number,
  nextDebitDate: Date,
  isActive: Boolean,
  failureCount: Number,
  maxRetries: 3,
  targetPlanId: ObjectId,
  lastSuccessfulDebit: Date
}
```

### 5. Round-Up Savings

#### Business Logic:
Round-up savings automatically saves spare change from card transactions. Every purchase is rounded up to the nearest ₦10/₦50/₦100 and the difference is saved.

**Implementation Flow:**
1. **Card Linking**: Connect user's primary spending card via Paystack
2. **Transaction Monitoring**: Receive real-time transaction webhooks
3. **Round-Up Calculation**: Round transaction to nearest configured amount
4. **Micro-Debit**: Charge card for the difference amount
5. **Savings Allocation**: Add rounded amount to designated savings plan
6. **Batch Processing**: Process round-ups in batches to reduce transaction costs

**Database Schema:**
```javascript
const RoundUpSchema = {
  userId: ObjectId,
  cardId: ObjectId,
  isActive: Boolean,
  roundUpRule: ['10', '50', '100'], // round to nearest
  totalRoundedUp: Number,
  lastTransaction: Date,
  targetPlanId: ObjectId,
  monthlyLimit: Number // prevent excessive round-ups
}
```

### 6. Investment Products

#### Business Logic:
Investment products allow users to grow wealth through financial instruments like treasury bills, mutual funds, and bonds. Higher potential returns with corresponding risks.

**Implementation Flow:**
1. **Risk Assessment**: User completes questionnaire to determine risk tolerance
2. **Product Catalog**: Display available investments with risk levels and historical returns
3. **Purchase Process**: Buy investment units using wallet funds
4. **Portfolio Management**: Track performance, dividends, and market value
5. **Liquidation**: Sell investments back to cash (subject to lock periods)
6. **Rebalancing**: Suggest portfolio adjustments based on performance

**Database Schema:**
```javascript
const InvestmentSchema = {
  userId: ObjectId,
  productType: ['treasury_bills', 'mutual_funds', 'bonds', 'stocks'],
  productName: String,
  amount: Number,
  units: Number,
  unitPrice: Number,
  currentValue: Number,
  totalReturns: Number,
  purchaseDate: Date,
  maturityDate: Date,
  riskLevel: ['low', 'medium', 'high'],
  status: ['active', 'matured', 'liquidated']
}
```

### 7. Referral System

#### Business Logic:
Referral system incentivizes user acquisition by rewarding both referrer and referee. Creates viral growth loop while building user base.

**Implementation Flow:**
1. **Code Generation**: Each user gets unique referral code upon registration
2. **Referral Tracking**: Track when new users sign up using referral codes
3. **Qualification Criteria**: Referee must complete KYC and save minimum amount (₦5,000)
4. **Reward Distribution**: Credit bonuses to both parties once criteria are met
5. **Tier System**: Higher rewards for users who refer more people
6. **Fraud Prevention**: Detect and prevent fake referrals and self-referrals

**Database Schema:**
```javascript
const ReferralSchema = {
  referrerId: ObjectId,
  refereeId: ObjectId,
  referralCode: String,
  status: ['pending', 'qualified', 'rewarded', 'expired'],
  referrerReward: Number,
  refereeReward: Number,
  qualificationDate: Date,
  rewardPaidDate: Date,
  createdAt: Date
}
```




```

### 9. Bill Payment Integration

#### Business Logic:
Bill payments add convenience and generate commission revenue. Users can pay utilities, buy airtime/data, and handle subscriptions from their savings wallet.

**Implementation Flow:**
1. **Provider Integration**: Connect with bill payment APIs (Paystack Bills, Flutterwave Bills)
2. **Service Categories**: Airtime, data, electricity, cable TV, internet, betting
3. **Beneficiary Management**: Save frequent payment recipients for quick access
4. **Balance Verification**: Ensure sufficient wallet balance before processing
5. **Commission Tracking**: Earn small percentage on each successful payment
6. **Transaction History**: Maintain detailed records for user reference

**Database Schema:**
```javascript
const BillPaymentSchema = {
  userId: ObjectId,
  billType: ['airtime', 'data', 'electricity', 'cable_tv', 'internet'],
  provider: String,
  amount: Number,
  beneficiary: String, // phone number, meter number, etc.
  reference: String,
  status: ['pending', 'successful', 'failed'],
  commission: Number,
  providerReference: String,
  createdAt: Date
}
```

### 10. Card Management System

#### Business Logic:
Card management enables users to save multiple payment methods securely and use them for various transactions within the app.

**Implementation Flow:**
1. **Card Addition**: Use Paystack tokenization to securely save card details
2. **Verification Process**: Charge small amount (₦100) and reverse to verify card
3. **Default Card Setting**: Allow users to set primary card for auto-debits
4. **Security Measures**: Store only tokens, never actual card numbers
5. **Card Status Management**: Handle expired, blocked, or invalid cards
6. **Usage Tracking**: Monitor card usage for fraud detection

**Database Schema:**
```javascript
const CardSchema = {
  userId: ObjectId,
  cardToken: String, // Paystack authorization code
  last4: String,
  brand: String, // visa, mastercard
  bank: String,
  isDefault: Boolean,
  isActive: Boolean,
  expiryMonth: String,
  expiryYear: String,
  isVerified: Boolean,
  createdAt: Date,
  lastUsed: Date
}
```

### 11. Interest Calculation Engine

#### Business Logic:
The interest calculation engine is the heart of the savings app, automatically calculating and distributing interest across all savings products daily.

**Implementation Flow:**
1. **Daily Cron Job**: Run at midnight to calculate previous day's interest
2. **Product-Specific Calculations**: Different formulas for flex, fixed deposits, targets
3. **Compound Interest**: Add calculated interest to principal for next day's calculation
4. **Pro-rata Calculations**: Handle partial days for new deposits/withdrawals
5. **Interest Distribution**: Credit interest to respective accounts
6. **Audit Trail**: Maintain detailed logs of all interest calculations

**Implementation Logic:**
```javascript
// Daily Interest Calculation Service
class InterestCalculationService {
  
  calculateFlexInterest(userId) {
    // Get user's flex balance
    // Calculate: balance * (annualRate / 365)
    // Add interest to flex balance
    // Create interest transaction record
  }
  
  calculateFixedDepositInterest(depositId) {
    // Get deposit details
    // Calculate compound interest based on days elapsed
    // Update accrued interest (don't add to principal until maturity)
    // Check if maturity date reached
  }
  
  calculateTargetSavingsInterest(planId) {
    // Get current saved amount
    // Calculate: savedAmount * (planRate / 365)
    // Add to saved amount
    // Check if target reached
  }
  
  processAllInterest() {
    // Run all calculations
    // Handle any errors gracefully
    // Send summary report to admin
    // Update user notifications
  }
}
```

---

## REQUIRED API ENDPOINTS

### Fixed Deposit Endpoints
POST /api/fixed-deposit/create
GET /api/fixed-deposit/user
POST /api/fixed-deposit/break/:id
GET /api/fixed-deposit/calculate-interest
PUT /api/fixed-deposit/:id/renew

### Investment Endpoints
GET /api/investments/products
POST /api/investments/buy
POST /api/investments/sell
GET /api/investments/portfolio
GET /api/investments/performance

### AutoSave Endpoints
POST /api/autosave/setup
PUT /api/autosave/:id/toggle
GET /api/autosave/user
POST /api/autosave/process-debits

### Card Management Endpoints
POST /api/cards/add
GET /api/cards/user
DELETE /api/cards/:id
PUT /api/cards/:id/set-default
POST /api/cards/charge

### Interest Calculation Endpoints
POST /api/interest/calculate
GET /api/interest/history/:userId
POST /api/interest/process-daily
GET /api/interest/rates

### Referral Endpoints
POST /api/referral/generate-code
POST /api/referral/use-code
GET /api/referral/stats/:userId
GET /api/referral/leaderboard

### Loan Endpoints
POST /api/loans/apply
GET /api/loans/eligibility/:userId
POST /api/loans/repay
GET /api/loans/schedule/:loanId

### Bill Payment Endpoints
GET /api/bills/providers/:type
POST /api/bills/validate
POST /api/bills/pay
GET /api/bills/history/:userId

---

## INFRASTRUCTURE REQUIREMENTS

### 1. Background Job Processing
Missing: Job Queue System (Bull/Agenda)
- Daily interest calculations
- AutoSave processing
- Loan repayment reminders
- Maturity notifications
- Failed transaction retries

### 2. Notification System
Enhanced notification system needed
- Push notifications (FCM)
- Email notifications (SendGrid/Mailgun)
- SMS notifications (Twilio/Termii)
- In-app notifications (real-time)

### 3. Analytics & Reporting
Missing: Analytics endpoints
GET /api/analytics/user-dashboard
GET /api/analytics/admin-dashboard
GET /api/analytics/financial-reports
GET /api/analytics/user-behavior

### 4. Security Enhancements
Missing security features
- Rate limiting (express-rate-limit)
- Input validation (Joi/Yup)
- SQL injection protection
- XSS protection
- CSRF protection
- API versioning

---

## DATABASE SCHEMA ENHANCEMENTS

### User Model Extensions
Add to existing User model
{
  savingsBalance: Number,
  investmentBalance: Number,
  loanBalance: Number,
  creditScore: Number,
  riskProfile: String,
  preferredCurrency: String,
  timezone: String,
  lastActiveDate: Date,
  referralCode: String,
  referredBy: ObjectId
}

### Transaction Model Extensions
Enhance existing Transaction model
{
  category: String,
  subcategory: String,
  merchantName: String,
  location: String,
  paymentMethod: String,
  fees: Number,
  exchangeRate: Number,
  originalAmount: Number,
  originalCurrency: String
}

---

## INTEGRATION REQUIREMENTS

### 1. Payment Providers
- Paystack (Current) - Enhanced features
- Flutterwave - Backup provider
- Monnify - Additional option

### 2. Banking APIs
- Mono - Account aggregation
- Okra - Banking data
- OnePipe - Multi-bank integration

### 3. Investment Platforms
- ARM Investment - Mutual funds
- Stanbic IBTC - Treasury bills
- CardinalStone - Bonds

### 4. Credit Scoring
- CreditRegistry - Credit reports
- FirstCentral - Credit scoring

---

## PERFORMANCE OPTIMIZATIONS

### 1. Caching Strategy
Redis implementation needed
- User session caching
- Frequently accessed data
- API response caching
- Rate limiting storage

### 2. Database Optimization
MongoDB optimizations
- Proper indexing strategy
- Aggregation pipelines
- Connection pooling
- Query optimization

---

## COMPLIANCE & SECURITY

### 1. Financial Regulations
- CBN compliance requirements
- AML (Anti-Money Laundering)
- KYC enhancement
- Transaction monitoring
- Suspicious activity reporting

### 2. Data Protection
- GDPR compliance
- Data encryption at rest
- PII data handling
- Audit logging
- Data retention policies

---

## IMPLEMENTATION PRIORITY

### Phase 1 (Critical - 4 weeks)
1. Fixed Deposit system
2. Enhanced interest calculation
3. Card management
4. AutoSave functionality

### Phase 2 (Important - 6 weeks)
1. Investment products
2. Referral system
3. Bill payments
4. Loan system

### Phase 3 (Enhancement - 8 weeks)
1. Advanced analytics
2. Multiple payment providers
3. Banking API integration
4. Mobile app APIs

---

## REVENUE STREAMS TO IMPLEMENT

1. Transaction Fees - Small fees on transfers
2. Investment Commissions - Percentage on investments
3. Loan Interest - Interest on quick loans
4. Bill Payment Commissions - Commission from billers
5. Premium Features - Advanced analytics, higher limits
6. Referral Bonuses - Incentivize user acquisition

---

This comprehensive analysis shows your backend is solid but needs significant enhancements to compete with PiggyVest, Cowrywise, and other leading savings apps. Focus on Phase 1 features first for immediate impact.
