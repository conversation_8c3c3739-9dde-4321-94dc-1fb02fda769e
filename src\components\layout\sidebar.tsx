
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { MainNav } from "../main-nav";

interface SidebarProps {
  isAdmin?: boolean;
  className?: string;
  collapsed?: boolean;
}

export function Sidebar({ isAdmin = false, className, collapsed: externalCollapsed }: SidebarProps) {
  const [internalCollapsed, setInternalCollapsed] = useState(false);
  const isMobile = useIsMobile();

  const collapsed = externalCollapsed !== undefined ? externalCollapsed : internalCollapsed;

  useEffect(() => {
    if (externalCollapsed === undefined) {
      setInternalCollapsed(isMobile);
    }
  }, [isMobile, externalCollapsed]);

  return (
    <div
      className={cn(
        "flex flex-col bg-brand-blue border-r border-brand-yellow/30 h-screen sticky top-0 transition-all duration-300 shadow-lg",
        collapsed ? "w-16" : "w-64",
        className
      )}
    >
      <div className="flex h-14 items-center px-4 border-b border-brand-yellow/20">
        <Link
          to={isAdmin ? "/admin/dashboard" : "/dashboard"}
          className="flex items-center gap-1 font-bold text-brand-yellow transition-all"
        >
          {!collapsed && (
            <>
              <div className="h-6 w-6 rounded-full overflow-hidden bg-white/10 backdrop-blur-sm border border-white/20 shadow-lg animate-pulse-light">
                <img
                  src="/logo-white-circular.svg"
                  alt="Better Interest Logo"
                  className="h-full w-full object-contain"
                />
              </div>
              <span className="text-base uppercase tracking-widest font-sans text-brand-yellow animate-fade-in">
                Better Interest
              </span>
            </>
          )}
          {collapsed && (
            <div className="h-6 w-6 rounded-full overflow-hidden bg-white/10 backdrop-blur-sm border border-white/20 shadow-lg mx-auto animate-bounce-gentle">
              <img
                src="/logo-white-circular.svg"
                alt="Better Interest Logo"
                className="h-full w-full object-contain"
              />
            </div>
          )}
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-6 px-3">
        <div className="rounded-xl bg-white/10 backdrop-blur-sm p-4 mb-4 border border-brand-yellow/10">
          <MainNav isAdmin={isAdmin} className="space-y-2 font-bold" collapsed={collapsed} />
        </div>
      </div>
      <div className="border-t border-brand-yellow/20 p-3">
        {externalCollapsed === undefined && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setInternalCollapsed(!internalCollapsed)}
            className="w-full justify-center bg-white/10 border-brand-yellow/30 text-brand-yellow hover:bg-brand-yellow hover:text-brand-blue transition-all font-unica-one tracking-wide"
          >
            {collapsed ? (
              <ChevronRight className="transition-colors" />
            ) : (
              <ChevronLeft className="transition-colors" />
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
