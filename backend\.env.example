# Database Configuration
MONGO_URI=mongodb://localhost:27017/kojapay_savings
# For MongoDB Atlas (Production):
# MONGO_URI=mongodb+srv://username:<EMAIL>/kojapay_savings?retryWrites=true&w=majority

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_complex
JWT_EXPIRES_IN=7d

# Paystack Configuration
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key_here
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key_here

# Email Configuration (Optional - for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# SMS Configuration (Optional - for OTP)
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=KojaPay

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Frontend URL (for CORS and redirects)
FRONTEND_URL=http://localhost:3000

# Cron Job API Key (for scheduled tasks)
CRON_API_KEY=your_secure_cron_api_key_here

# KYC Configuration
DOJAH_API_KEY=your_dojah_api_key
DOJAH_APP_ID=your_dojah_app_id

# Interest Rates Configuration
DEFAULT_FLEX_INTEREST_RATE=15
DEFAULT_FIXED_DEPOSIT_RATE=15
DEFAULT_SAFELOCK_BASE_RATE=18
DEFAULT_TARGET_SAVINGS_RATE=10

# Referral Configuration
REFERRER_REWARD_AMOUNT=500
REFEREE_REWARD_AMOUNT=200
REFERRAL_QUALIFICATION_AMOUNT=5000
REFERRAL_QUALIFICATION_DAYS=7

# AutoSave Configuration
AUTOSAVE_MIN_AMOUNT=100
AUTOSAVE_MAX_RETRIES=3

# Card Management
CARD_VERIFICATION_AMOUNT=100

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure_admin_password_here

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Redis Configuration (Optional - for caching)
REDIS_URL=redis://localhost:6379

# Webhook Configuration
PAYSTACK_WEBHOOK_SECRET=your_paystack_webhook_secret

# Development Configuration
DEBUG=true
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000
