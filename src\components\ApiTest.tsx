import { API_CONFIG } from '@/config/env';
import { useEffect, useState } from 'react';

export function ApiTest() {
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApiConnection = async () => {
    setIsLoading(true);
    setError(null);
    
    console.log('🔍 Testing API connection to:', `${API_CONFIG.BASE_URL}/health`);
    
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/health`);
      
      console.log('📡 API Test Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('✅ API Test Success:', data);
      setTestResult(data);
    } catch (err: any) {
      console.error('❌ API Test Error:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Test API connection on component mount
    testApiConnection();
  }, []);

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-semibold text-gray-800">API Status</h3>
        <button
          onClick={testApiConnection}
          disabled={isLoading}
          className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Test'}
        </button>
      </div>
      
      <div className="text-xs space-y-1">
        <div>
          <span className="font-medium">Endpoint:</span>
          <div className="text-gray-600 break-all">{API_CONFIG.BASE_URL}</div>
        </div>
        
        {isLoading && (
          <div className="text-blue-600">🔄 Testing connection...</div>
        )}
        
        {error && (
          <div className="text-red-600">
            <div className="font-medium">❌ Error:</div>
            <div>{error}</div>
          </div>
        )}
        
        {testResult && (
          <div className="text-green-600">
            <div className="font-medium">✅ Connected:</div>
            <div>{testResult.message}</div>
            <div className="text-gray-500 text-xs mt-1">
              {new Date(testResult.timestamp).toLocaleTimeString()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
