const User = require('./user');
const SavingsPlan = require('./savingsPlan');
const Transaction = require('./transaction');
const Accounts = require('./accounts');
const Deposit = require('./deposit');
const GlobalSettings = require('./globalSettings');
const GroupSavingsPlan = require('./groupSavingsPlan');
const KYC = require('./kyc');
const Notification = require('./notification');
const Profit = require('./profit');
const RotationalGroupSavings = require('./rotationalGroupSavings');
const Withdraw = require('./withdraw');
const WithdrawAccounts = require('./withdrawAccounts');
// New models for enhanced features
const FixedDeposit = require('./fixedDeposit');
const FlexSavings = require('./flexSavings');
const SafeLock = require('./safeLock');
const Card = require('./card');
const AutoSave = require('./autoSave');
const Referral = require('./referral');

module.exports = {
  User,
  SavingsPlan,
  Transaction,
  Accounts,
  Deposit,
  GlobalSettings,
  GroupSavingsPlan,
  KYC,
  Notification,
  Profit,
  RotationalGroupSavings,
  Withdraw,
  WithdrawAccounts,
  // New models
  FixedDeposit,
  FlexSavings,
  SafeLock,
  Card,
  AutoSave,
  Referral,
};
