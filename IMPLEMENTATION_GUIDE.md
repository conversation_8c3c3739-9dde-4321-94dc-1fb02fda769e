# Kojapay Savings App - Complete Implementation Guide

## Overview
This guide provides step-by-step instructions to transform your current savings app into a world-class platform that can compete with PiggyVest, Cowrywise, and other leading fintech apps.

## Phase 1: Backend Database Schema Enhancement (Priority 1)

### 1.1 Update User Model
**File: `backend/models/user.js`**

Add these fields to your existing User schema:

```javascript
// Enhanced balance tracking
savingsBalance: {
  type: Number,
  default: 0,
},
investmentBalance: {
  type: Number,
  default: 0,
},
loanBalance: {
  type: Number,
  default: 0,
},
// Credit and risk assessment
creditScore: {
  type: Number,
  default: 0,
},
riskProfile: {
  type: String,
  enum: ['low', 'medium', 'high'],
  default: 'low',
},
// User preferences
preferredCurrency: {
  type: String,
  default: 'NGN',
},
timezone: {
  type: String,
  default: 'Africa/Lagos',
},
lastActiveDate: {
  type: Date,
  default: Date.now,
},
// Referral system
referralCode: {
  type: String,
  unique: true,
  sparse: true,
},
referredBy: {
  type: mongoose.Schema.Types.ObjectId,
  ref: 'User',
  default: null,
},
```

### 1.2 Create Fixed Deposit Model
**File: `backend/models/fixedDeposit.js`**

```javascript
const mongoose = require('mongoose');

const FixedDepositSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 10000, // Minimum ₦10,000
  },
  duration: {
    type: Number,
    required: true, // in days
  },
  interestRate: {
    type: Number,
    required: true, // annual percentage
  },
  startDate: {
    type: Date,
    default: Date.now,
  },
  maturityDate: {
    type: Date,
    required: true,
  },
  accruedInterest: {
    type: Number,
    default: 0,
  },
  status: {
    type: String,
    enum: ['active', 'matured', 'broken'],
    default: 'active',
  },
  penaltyRate: {
    type: Number,
    default: 25, // 25% of accrued interest
  },
  autoRenewal: {
    type: Boolean,
    default: false,
  },
  compoundingFrequency: {
    type: String,
    default: 'daily',
  },
  lastInterestCalculation: {
    type: Date,
    default: Date.now,
  },
}, { timestamps: true });

module.exports = mongoose.model('FixedDeposit', FixedDepositSchema);
```

### 1.3 Create Flex Savings Model
**File: `backend/models/flexSavings.js`**

```javascript
const mongoose = require('mongoose');

const FlexSavingsSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true, // One flex account per user
  },
  balance: {
    type: Number,
    default: 0,
  },
  dailyInterestRate: {
    type: Number,
    default: 0.0411, // 15% annually / 365 days
  },
  lastInterestCalculation: {
    type: Date,
    default: Date.now,
  },
  totalInterestEarned: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, { timestamps: true });

module.exports = mongoose.model('FlexSavings', FlexSavingsSchema);
```

### 1.4 Create SafeLock Model
**File: `backend/models/safeLock.js`**

```javascript
const mongoose = require('mongoose');

const SafeLockSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 50000, // Minimum ₦50,000
  },
  lockDuration: {
    type: Number,
    required: true, // in days
  },
  interestRate: {
    type: Number,
    required: true, // 18-25% annually
  },
  startDate: {
    type: Date,
    default: Date.now,
  },
  maturityDate: {
    type: Date,
    required: true,
  },
  canBreak: {
    type: Boolean,
    default: false,
  },
  breakagePenalty: {
    type: Number,
    default: 50, // 50% of principal
  },
  status: {
    type: String,
    enum: ['active', 'matured', 'broken'],
    default: 'active',
  },
  maturityBonus: {
    type: Number,
    default: 0,
  },
}, { timestamps: true });

module.exports = mongoose.model('SafeLock', SafeLockSchema);
```

### 1.5 Create AutoSave Model
**File: `backend/models/autoSave.js`**

```javascript
const mongoose = require('mongoose');

const AutoSaveSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  cardId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Card',
    required: true,
  },
  frequency: {
    type: String,
    enum: ['daily', 'weekly', 'monthly'],
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 100, // Minimum ₦100
  },
  nextDebitDate: {
    type: Date,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  failureCount: {
    type: Number,
    default: 0,
  },
  maxRetries: {
    type: Number,
    default: 3,
  },
  targetPlanId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SavingsPlan',
    default: null,
  },
  lastSuccessfulDebit: {
    type: Date,
    default: null,
  },
}, { timestamps: true });

module.exports = mongoose.model('AutoSave', AutoSaveSchema);
```

## Phase 2: API Routes Implementation

### 2.1 Fixed Deposit Routes
**File: `backend/routes/fixedDepositRoutes.js`**

```javascript
const express = require('express');
const router = express.Router();
const FixedDeposit = require('../models/fixedDeposit');
const User = require('../models/user');
const Transaction = require('../models/transaction');
const { authenticateToken } = require('../middleware/authMiddleware');

// Create Fixed Deposit
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const { amount, duration } = req.body;
    const userId = req.user.id;

    // Validate minimum amount
    if (amount < 10000) {
      return res.status(400).json({ 
        error: 'Minimum fixed deposit amount is ₦10,000' 
      });
    }

    // Check user balance
    const user = await User.findById(userId);
    if (user.balance < amount) {
      return res.status(400).json({ 
        error: 'Insufficient balance' 
      });
    }

    // Interest rates based on duration
    const interestRates = {
      30: 12, 60: 14, 90: 16, 180: 18, 365: 20
    };

    const interestRate = interestRates[duration];
    if (!interestRate) {
      return res.status(400).json({ 
        error: 'Invalid duration. Choose from 30, 60, 90, 180, or 365 days' 
      });
    }

    // Calculate maturity date
    const maturityDate = new Date();
    maturityDate.setDate(maturityDate.getDate() + duration);

    // Create fixed deposit
    const fixedDeposit = new FixedDeposit({
      userId,
      amount,
      duration,
      interestRate,
      maturityDate,
    });

    await fixedDeposit.save();

    // Deduct from user balance
    user.balance -= amount;
    await user.save();

    // Create transaction record
    const transaction = new Transaction({
      userId,
      type: 'fixed_deposit',
      amount: -amount,
      description: `Fixed deposit created for ${duration} days`,
      balanceAfter: user.balance,
      reference: `FD_${fixedDeposit._id}`,
    });

    await transaction.save();

    res.status(201).json({
      message: 'Fixed deposit created successfully',
      fixedDeposit,
    });

  } catch (error) {
    console.error('Error creating fixed deposit:', error);
    res.status(500).json({ error: 'Failed to create fixed deposit' });
  }
});

// Get user's fixed deposits
router.get('/user', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const fixedDeposits = await FixedDeposit.find({ userId })
      .sort({ createdAt: -1 });

    res.json({ fixedDeposits });
  } catch (error) {
    console.error('Error fetching fixed deposits:', error);
    res.status(500).json({ error: 'Failed to fetch fixed deposits' });
  }
});

module.exports = router;
```

This is just the beginning of the implementation. The complete guide would include:

1. All remaining models (Card, RoundUp, Investment, Referral, etc.)
2. Complete API routes for all features
3. Interest calculation engine with cron jobs
4. Frontend component updates
5. Security enhancements
6. Testing strategies

Would you like me to continue with specific sections or would you prefer to implement this phase by phase?
