const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/user');
const GlobalSettings = require('../models/globalSettings');

async function setupDatabase() {
  try {
    console.log('🚀 Starting database setup...');
    
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/kojapay_savings';
    console.log(`📡 Connecting to MongoDB: ${mongoUri}`);
    
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB successfully');

    // Create indexes for better performance
    console.log('📊 Creating database indexes...');
    await createIndexes();

    // Create default admin user
    console.log('👤 Creating default admin user...');
    await createDefaultAdmin();

    // Create default global settings
    console.log('⚙️ Creating default global settings...');
    await createDefaultSettings();

    // Create sample data for development
    if (process.env.NODE_ENV === 'development') {
      console.log('🧪 Creating sample data for development...');
      await createSampleData();
    }

    console.log('🎉 Database setup completed successfully!');
    console.log('\n📋 Setup Summary:');
    console.log('- Database indexes created');
    console.log('- Default admin user created');
    console.log('- Global settings configured');
    if (process.env.NODE_ENV === 'development') {
      console.log('- Sample development data created');
    }
    
    console.log('\n🔐 Default Admin Credentials:');
    console.log(`Email: ${process.env.ADMIN_EMAIL || '<EMAIL>'}`);
    console.log(`Password: ${process.env.ADMIN_PASSWORD || 'KojaPay2024Admin!'}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

async function createIndexes() {
  try {
    // User indexes
    await User.collection.createIndex({ email: 1 }, { unique: true });
    await User.collection.createIndex({ phone: 1 }, { unique: true, sparse: true });
    await User.collection.createIndex({ referralCode: 1 }, { unique: true, sparse: true });
    await User.collection.createIndex({ createdAt: 1 });
    await User.collection.createIndex({ role: 1 });
    await User.collection.createIndex({ kycStatus: 1 });

    // Transaction indexes (if Transaction model exists)
    try {
      const Transaction = require('../models/transaction');
      await Transaction.collection.createIndex({ userId: 1 });
      await Transaction.collection.createIndex({ type: 1 });
      await Transaction.collection.createIndex({ createdAt: -1 });
      await Transaction.collection.createIndex({ reference: 1 }, { unique: true });
      await Transaction.collection.createIndex({ userId: 1, createdAt: -1 });
    } catch (err) {
      console.log('⚠️ Transaction model not found, skipping transaction indexes');
    }

    // Savings Plan indexes (if model exists)
    try {
      const SavingsPlan = require('../models/savingsPlan');
      await SavingsPlan.collection.createIndex({ userId: 1 });
      await SavingsPlan.collection.createIndex({ createdAt: -1 });
      await SavingsPlan.collection.createIndex({ status: 1 });
    } catch (err) {
      console.log('⚠️ SavingsPlan model not found, skipping savings plan indexes');
    }

    // Fixed Deposit indexes
    try {
      const FixedDeposit = require('../models/fixedDeposit');
      await FixedDeposit.collection.createIndex({ userId: 1 });
      await FixedDeposit.collection.createIndex({ status: 1 });
      await FixedDeposit.collection.createIndex({ maturityDate: 1 });
      await FixedDeposit.collection.createIndex({ reference: 1 }, { unique: true });
    } catch (err) {
      console.log('⚠️ FixedDeposit model not found, skipping fixed deposit indexes');
    }

    // Flex Savings indexes
    try {
      const FlexSavings = require('../models/flexSavings');
      await FlexSavings.collection.createIndex({ userId: 1 }, { unique: true });
      await FlexSavings.collection.createIndex({ lastInterestCalculation: 1 });
    } catch (err) {
      console.log('⚠️ FlexSavings model not found, skipping flex savings indexes');
    }

    // SafeLock indexes
    try {
      const SafeLock = require('../models/safeLock');
      await SafeLock.collection.createIndex({ userId: 1 });
      await SafeLock.collection.createIndex({ status: 1 });
      await SafeLock.collection.createIndex({ maturityDate: 1 });
      await SafeLock.collection.createIndex({ reference: 1 }, { unique: true });
    } catch (err) {
      console.log('⚠️ SafeLock model not found, skipping SafeLock indexes');
    }

    // Card indexes
    try {
      const Card = require('../models/card');
      await Card.collection.createIndex({ userId: 1 });
      await Card.collection.createIndex({ authorizationCode: 1 }, { unique: true });
      await Card.collection.createIndex({ isDefault: 1, userId: 1 });
    } catch (err) {
      console.log('⚠️ Card model not found, skipping card indexes');
    }

    // Referral indexes
    try {
      const Referral = require('../models/referral');
      await Referral.collection.createIndex({ referrerId: 1 });
      await Referral.collection.createIndex({ refereeId: 1 });
      await Referral.collection.createIndex({ referralCode: 1 });
      await Referral.collection.createIndex({ status: 1 });
    } catch (err) {
      console.log('⚠️ Referral model not found, skipping referral indexes');
    }

    console.log('✅ Database indexes created successfully');
  } catch (error) {
    console.error('❌ Error creating indexes:', error);
    throw error;
  }
}

async function createDefaultAdmin() {
  try {
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'KojaPay2024Admin!';

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: adminEmail });
    if (existingAdmin) {
      console.log('⚠️ Admin user already exists, skipping creation');
      return;
    }

    // Create admin user
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    const crypto = require('crypto');
    
    const adminUser = new User({
      firstName: 'Admin',
      lastName: 'User',
      email: adminEmail,
      password: hashedPassword,
      phoneNumber: '+2348000000000', // Default admin phone number
      role: 'admin',
      isActive: true,
      kycStatus: 'APPROVED',
      referralCode: crypto.randomBytes(4).toString('hex').toUpperCase(),
      balance: 0,
      savingsBalance: 0,
      totalEarnings: 0,
    });

    await adminUser.save();
    console.log('✅ Default admin user created successfully');
  } catch (error) {
    console.error('❌ Error creating default admin:', error);
    throw error;
  }
}

async function createDefaultSettings() {
  try {
    // Check if settings already exist
    const existingSettings = await GlobalSettings.findOne();
    if (existingSettings) {
      console.log('⚠️ Global settings already exist, skipping creation');
      return;
    }

    // Get the admin user to use as lastUpdatedBy
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminUser = await User.findOne({ email: adminEmail });

    if (!adminUser) {
      console.log('⚠️ Admin user not found, creating settings without lastUpdatedBy');
    }

    // Use the static method to create settings with proper lastUpdatedBy
    const settings = await GlobalSettings.getSettings(adminUser ? adminUser._id : null);

    // Update the settings with our default values
    const defaultUpdates = {
      interestRates: {
        flexSavings: parseFloat(process.env.DEFAULT_FLEX_INTEREST_RATE) || 15,
        fixedDeposit: parseFloat(process.env.DEFAULT_FIXED_DEPOSIT_RATE) || 15,
        safeLock: parseFloat(process.env.DEFAULT_SAFELOCK_BASE_RATE) || 18,
        targetSavings: parseFloat(process.env.DEFAULT_TARGET_SAVINGS_RATE) || 10,
      },
      fees: {
        withdrawalFee: 0,
        transferFee: 0,
        cardChargeFee: 1.5, // 1.5% for card charges
      },
      limits: {
        dailyWithdrawalLimit: 1000000, // ₦1M
        monthlyWithdrawalLimit: 10000000, // ₦10M
        minimumDeposit: 100,
        maximumDeposit: 50000000, // ₦50M
      },
      referralSettings: {
        referrerReward: parseFloat(process.env.REFERRER_REWARD_AMOUNT) || 500,
        refereeReward: parseFloat(process.env.REFEREE_REWARD_AMOUNT) || 200,
        qualificationAmount: parseFloat(process.env.REFERRAL_QUALIFICATION_AMOUNT) || 5000,
        qualificationDays: parseInt(process.env.REFERRAL_QUALIFICATION_DAYS) || 7,
      },
      autoSaveSettings: {
        minimumAmount: parseFloat(process.env.AUTOSAVE_MIN_AMOUNT) || 100,
        maximumRetries: parseInt(process.env.AUTOSAVE_MAX_RETRIES) || 3,
      },
      kycSettings: {
        requireKycForWithdrawal: true,
        kycWithdrawalLimit: 50000, // ₦50K without KYC
        autoApproveKyc: false,
      },
      maintenanceMode: false,
      appVersion: '1.0.0',
    };

    // Use the updateSettings method to properly set the values
    await GlobalSettings.updateSettings(defaultUpdates, adminUser ? adminUser._id : null);
    console.log('✅ Default global settings created successfully');
  } catch (error) {
    console.error('❌ Error creating default settings:', error);
    throw error;
  }
}

async function createSampleData() {
  try {
    // Create sample users for development
    const sampleUsers = [
      {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 12),
        phoneNumber: '+2348012345678',
        balance: 50000,
        savingsBalance: 25000,
        kycStatus: 'APPROVED',
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 12),
        phoneNumber: '+2348087654321',
        balance: 75000,
        savingsBalance: 40000,
        kycStatus: 'PENDING',
      },
    ];

    for (const userData of sampleUsers) {
      const existingUser = await User.findOne({ email: userData.email });
      if (!existingUser) {
        const crypto = require('crypto');
        userData.referralCode = crypto.randomBytes(4).toString('hex').toUpperCase();
        const user = new User(userData);
        await user.save();
        console.log(`✅ Sample user created: ${userData.email}`);
      }
    }

    console.log('✅ Sample data created successfully');
  } catch (error) {
    console.error('❌ Error creating sample data:', error);
    throw error;
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
