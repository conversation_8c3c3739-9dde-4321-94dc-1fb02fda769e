const mongoose = require('mongoose');

const FixedDepositSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 10000, // Minimum ₦10,000
  },
  duration: {
    type: Number,
    required: true, // in days
  },
  interestRate: {
    type: Number,
    required: true, // annual percentage
  },
  startDate: {
    type: Date,
    default: Date.now,
  },
  maturityDate: {
    type: Date,
    required: true,
  },
  accruedInterest: {
    type: Number,
    default: 0,
  },
  status: {
    type: String,
    enum: ['active', 'matured', 'broken'],
    default: 'active',
  },
  penaltyRate: {
    type: Number,
    default: 25, // 25% of accrued interest forfeited on early termination
  },
  autoRenewal: {
    type: Boolean,
    default: false,
  },
  compoundingFrequency: {
    type: String,
    default: 'daily',
  },
  lastInterestCalculation: {
    type: Date,
    default: Date.now,
  },
  // Track if this deposit was auto-renewed
  isRenewal: {
    type: Boolean,
    default: false,
  },
  originalDepositId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FixedDeposit',
    default: null,
  },
}, { timestamps: true });

// Index for efficient queries
FixedDepositSchema.index({ userId: 1, status: 1 });
FixedDepositSchema.index({ maturityDate: 1, status: 1 });

// Virtual for calculating current value
FixedDepositSchema.virtual('currentValue').get(function() {
  return this.amount + this.accruedInterest;
});

// Method to calculate interest for a given period
FixedDepositSchema.methods.calculateInterest = function(days = 1) {
  const dailyRate = this.interestRate / 365 / 100;
  return this.amount * dailyRate * days;
};

// Method to check if deposit has matured
FixedDepositSchema.methods.hasMatured = function() {
  return new Date() >= this.maturityDate;
};

// Method to break deposit early
FixedDepositSchema.methods.breakEarly = function() {
  if (this.status !== 'active') {
    throw new Error('Can only break active deposits');
  }
  
  // Apply penalty
  const penalty = (this.accruedInterest * this.penaltyRate) / 100;
  const finalInterest = this.accruedInterest - penalty;
  
  this.status = 'broken';
  this.accruedInterest = finalInterest;
  
  return {
    principal: this.amount,
    interest: finalInterest,
    penalty: penalty,
    total: this.amount + finalInterest
  };
};

module.exports = mongoose.model('FixedDeposit', FixedDepositSchema);
