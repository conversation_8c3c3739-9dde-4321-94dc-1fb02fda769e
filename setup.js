#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 KojaPay Savings App - Quick Setup Script');
console.log('==========================================\n');

// Check if Node.js version is compatible
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.error('❌ Node.js version 16 or higher is required');
  console.error(`Current version: ${nodeVersion}`);
  process.exit(1);
}

console.log(`✅ Node.js version: ${nodeVersion}`);

// Function to run commands
function runCommand(command, description) {
  console.log(`\n📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

// Function to check if file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Function to create file if it doesn't exist
function createFileIfNotExists(filePath, content) {
  if (!fileExists(filePath)) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Created ${filePath}`);
  } else {
    console.log(`⚠️  ${filePath} already exists, skipping`);
  }
}

async function setup() {
  try {
    // 1. Install backend dependencies
    if (fileExists('./backend/package.json')) {
      process.chdir('./backend');
      runCommand('npm install', 'Installing backend dependencies');
      process.chdir('..');
    } else {
      console.log('⚠️  Backend package.json not found, skipping backend setup');
    }

    // 2. Install frontend dependencies
    if (fileExists('./package.json')) {
      runCommand('npm install', 'Installing frontend dependencies');
    } else {
      console.log('⚠️  Frontend package.json not found, skipping frontend setup');
    }

    // 3. Check MongoDB connection
    console.log('\n🗄️  Checking MongoDB...');
    try {
      execSync('mongod --version', { stdio: 'pipe' });
      console.log('✅ MongoDB is installed');
    } catch (error) {
      console.log('⚠️  MongoDB not found. Please install MongoDB:');
      console.log('   - Windows: https://docs.mongodb.com/manual/tutorial/install-mongodb-on-windows/');
      console.log('   - macOS: brew install mongodb-community');
      console.log('   - Linux: https://docs.mongodb.com/manual/administration/install-on-linux/');
      console.log('   - Or use MongoDB Atlas: https://www.mongodb.com/cloud/atlas');
    }

    // 4. Setup environment files
    console.log('\n⚙️  Setting up environment files...');
    
    // Backend .env
    const backendEnvContent = `# Database Configuration
MONGO_URI=mongodb://localhost:27017/kojapay_savings

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=kojapay_super_secret_jwt_key_2024_development_only_change_in_production
JWT_EXPIRES_IN=7d

# Paystack Configuration (Test Keys)
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key_here
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key_here

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Cron Job API Key
CRON_API_KEY=kojapay_cron_secret_key_2024

# Interest Rates Configuration
DEFAULT_FLEX_INTEREST_RATE=15
DEFAULT_FIXED_DEPOSIT_RATE=15
DEFAULT_SAFELOCK_BASE_RATE=18
DEFAULT_TARGET_SAVINGS_RATE=10

# Referral Configuration
REFERRER_REWARD_AMOUNT=500
REFEREE_REWARD_AMOUNT=200
REFERRAL_QUALIFICATION_AMOUNT=5000
REFERRAL_QUALIFICATION_DAYS=7

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=KojaPay2024Admin!

# Development Configuration
DEBUG=true
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000`;

    createFileIfNotExists('./backend/.env', backendEnvContent);

    // Frontend .env
    const frontendEnvContent = `# Frontend Environment Variables

# API Configuration
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_BACKEND_URL=http://localhost:3001

# Paystack Configuration (Public Key)
REACT_APP_PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key_here

# App Configuration
REACT_APP_NAME=KojaPay Savings
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# Development Configuration
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=debug`;

    createFileIfNotExists('./.env', frontendEnvContent);

    // 5. Setup database
    console.log('\n🗄️  Setting up database...');
    if (fileExists('./backend/scripts/setup-database.js')) {
      process.chdir('./backend');
      runCommand('node scripts/setup-database.js', 'Setting up database');
      process.chdir('..');
    } else {
      console.log('⚠️  Database setup script not found');
    }

    // 6. Create start scripts
    console.log('\n📝 Creating start scripts...');
    
    const startBackendScript = `#!/bin/bash
echo "🚀 Starting KojaPay Backend..."
cd backend
npm run dev`;

    const startFrontendScript = `#!/bin/bash
echo "🚀 Starting KojaPay Frontend..."
npm run dev`;

    const startBothScript = `#!/bin/bash
echo "🚀 Starting KojaPay Savings App..."
echo "Starting backend..."
cd backend && npm run dev &
BACKEND_PID=$!
cd ..
echo "Starting frontend..."
npm run dev &
FRONTEND_PID=$!

echo "✅ Both servers started!"
echo "Backend PID: $BACKEND_PID"
echo "Frontend PID: $FRONTEND_PID"
echo ""
echo "🌐 Frontend: http://localhost:3000"
echo "🔗 Backend API: http://localhost:3001/api"
echo "👤 Admin Login: <EMAIL> / KojaPay2024Admin!"
echo ""
echo "Press Ctrl+C to stop both servers"

wait`;

    fs.writeFileSync('./start-backend.sh', startBackendScript);
    fs.writeFileSync('./start-frontend.sh', startFrontendScript);
    fs.writeFileSync('./start-app.sh', startBothScript);

    // Make scripts executable on Unix systems
    if (process.platform !== 'win32') {
      execSync('chmod +x start-backend.sh start-frontend.sh start-app.sh');
    }

    console.log('✅ Created start scripts');

    // 7. Final instructions
    console.log('\n🎉 Setup completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Update Paystack keys in environment files');
    console.log('2. Start MongoDB service');
    console.log('3. Run the application:');
    console.log('   - Backend only: ./start-backend.sh (or npm run dev in backend folder)');
    console.log('   - Frontend only: ./start-frontend.sh (or npm run dev in root folder)');
    console.log('   - Both servers: ./start-app.sh');
    console.log('\n🔗 URLs:');
    console.log('   - Frontend: http://localhost:3000');
    console.log('   - Backend API: http://localhost:3001/api');
    console.log('\n👤 Default Admin Login:');
    console.log('   - Email: <EMAIL>');
    console.log('   - Password: KojaPay2024Admin!');
    console.log('\n📚 Documentation:');
    console.log('   - API Endpoints: Check backend/routes/ folder');
    console.log('   - Environment Variables: backend/.env.example');
    console.log('   - Setup Summary: COMPREHENSIVE_FIXES_SUMMARY.md');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup
setup();
