import { API_CONFIG, PAYSTACK_CONFIG, APP_CONFIG } from '@/config/env';
import { ReactNode, useEffect, useState } from 'react';

interface EnvValidatorProps {
  children: ReactNode;
}

export function EnvValidator({ children }: EnvValidatorProps) {
  const [isValid, setIsValid] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    const validationErrors: string[] = [];

    // Validate API Configuration
    if (!API_CONFIG.BASE_URL) {
      validationErrors.push('API_CONFIG.BASE_URL is not configured');
    }

    if (!API_CONFIG.BACKEND_URL) {
      validationErrors.push('API_CONFIG.BACKEND_URL is not configured');
    }

    // Validate Paystack Configuration
    if (!PAYSTACK_CONFIG.PUBLIC_KEY) {
      validationErrors.push('PAYSTACK_CONFIG.PUBLIC_KEY is not configured');
    }

    // Log configuration for debugging
    console.log('🔧 Environment Validation:', {
      API_CONFIG,
      PAYSTACK_CONFIG,
      APP_CONFIG,
      errors: validationErrors
    });

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      setIsValid(false);
      console.error('❌ Environment validation failed:', validationErrors);
    } else {
      setErrors([]);
      setIsValid(true);
      console.log('✅ Environment validation passed');
    }
  }, []);

  if (!isValid && errors.length > 0) {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md w-full">
          <div className="text-red-600 text-center mb-4">
            <h1 className="text-xl font-bold">Configuration Error</h1>
            <p className="text-sm mt-2">
              Please check your environment variables
            </p>
          </div>
          <div className="space-y-2">
            {errors.map((error, index) => (
              <div key={index} className="bg-red-100 border border-red-300 rounded p-2">
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <p className="text-gray-600 text-xs">
              Check the browser console for more details
            </p>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
