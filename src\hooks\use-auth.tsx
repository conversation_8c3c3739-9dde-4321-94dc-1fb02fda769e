import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { useToast } from './use-toast';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'user' | 'admin';
  kycStatus: 'pending' | 'verified' | 'rejected';
  profile?: {
    first_name: string;
    last_name: string;
    avatar_url?: string;
    status?: 'active' | 'blocked' | 'suspended';
    kyc_status?: 'pending' | 'verified' | 'rejected';
  };
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isLoading: boolean;
  isAdmin: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, firstName: string, lastName: string, phone?: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const isAdmin = user?.role === 'admin';
  const isLoading = loading;

  useEffect(() => {
    // Check for stored user data
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    setLoading(false);
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);

      console.log('🔐 AuthProvider: Attempting login with real API');

      // Use real API call instead of mock data
      const response = await fetch('http://localhost:3002/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      console.log('📡 AuthProvider: API Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Login failed' }));
        throw new Error(errorData.message || 'Login failed');
      }

      const data = await response.json();
      console.log('✅ AuthProvider: Login successful:', data);

      const user: User = {
        id: data.user.id || data.user._id,
        email: data.user.email,
        firstName: data.user.firstName || data.user.first_name,
        lastName: data.user.lastName || data.user.last_name,
        role: data.user.role || 'user',
        kycStatus: data.user.kycStatus || data.user.kyc_status || 'pending',
        profile: {
          first_name: data.user.firstName || data.user.first_name,
          last_name: data.user.lastName || data.user.last_name,
          status: data.user.status || 'active',
          kyc_status: data.user.kycStatus || data.user.kyc_status || 'pending'
        }
      };

      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('token', data.token);
      setUser(user);

      toast({
        title: "Welcome back!",
        description: "Successfully signed in to your account.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sign in. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, firstName: string, lastName: string, phone?: string) => {
    try {
      setLoading(true);

      console.log('📝 AuthProvider: Attempting registration with real API');

      // Use real API call instead of mock data
      const response = await fetch('http://localhost:3002/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, firstName, lastName, phone }),
      });

      console.log('📡 AuthProvider: Registration API Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Registration failed' }));
        throw new Error(errorData.message || 'Registration failed');
      }

      const data = await response.json();
      console.log('✅ AuthProvider: Registration successful:', data);

      const newUser: User = {
        id: data.user.id || data.user._id,
        email: data.user.email,
        firstName: data.user.firstName || data.user.first_name,
        lastName: data.user.lastName || data.user.last_name,
        role: data.user.role || 'user',
        kycStatus: data.user.kycStatus || data.user.kyc_status || 'pending',
        profile: {
          first_name: data.user.firstName || data.user.first_name,
          last_name: data.user.lastName || data.user.last_name,
          status: data.user.status || 'active',
          kyc_status: data.user.kycStatus || data.user.kyc_status || 'pending'
        }
      };

      localStorage.setItem('user', JSON.stringify(newUser));
      localStorage.setItem('token', data.token);
      setUser(newUser);

      toast({
        title: "Account created!",
        description: "Welcome to KojaPay. Please complete your KYC verification.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create account. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    setUser(null);
    toast({
      title: "Signed out",
      description: "You have been successfully signed out.",
    });
  };

  const updateProfile = async (data: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...data };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  };

  const refreshUser = async () => {
    // Mock refresh - in real app, fetch from backend
    if (user) {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      isLoading,
      isAdmin,
      signIn,
      signUp,
      signOut,
      updateProfile,
      refreshUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};
