const express = require('express');
const router = express.Router();
const Card = require('../models/card');
const User = require('../models/user');
const Transaction = require('../models/transaction');
const { authenticateToken } = require('../middleware/authMiddleware');
const axios = require('axios');

const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;
const PAYSTACK_BASE_URL = 'https://api.paystack.co';

// POST /api/cards/add - Add a new card via Paystack
router.post('/add', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { email, amount = 100 } = req.body; // Small amount for card verification

    if (!email) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email is required for card verification' 
      });
    }

    // Initialize Paystack transaction for card tokenization
    const paystackResponse = await axios.post(`${PAYSTACK_BASE_URL}/transaction/initialize`, {
      amount: amount * 100, // Convert to kobo
      email,
      metadata: { 
        userId,
        purpose: 'card_verification',
        custom_fields: [
          {
            display_name: "Card Verification",
            variable_name: "card_verification",
            value: "true"
          }
        ]
      },
      currency: 'NGN',
    }, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!paystackResponse.data.status) {
      return res.status(400).json({ 
        success: false, 
        message: 'Failed to initialize card verification',
        error: paystackResponse.data.message 
      });
    }

    res.json({
      success: true,
      message: 'Card verification initialized',
      data: {
        authorizationUrl: paystackResponse.data.data.authorization_url,
        reference: paystackResponse.data.data.reference,
        amount: amount
      }
    });

  } catch (error) {
    console.error('Error adding card:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/cards/verify - Verify and save card after Paystack callback
router.post('/verify', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { reference } = req.body;

    if (!reference) {
      return res.status(400).json({ 
        success: false, 
        message: 'Transaction reference is required' 
      });
    }

    // Verify transaction with Paystack
    const verifyResponse = await axios.get(`${PAYSTACK_BASE_URL}/transaction/verify/${reference}`, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
      },
    });

    if (!verifyResponse.data.status || verifyResponse.data.data.status !== 'success') {
      return res.status(400).json({ 
        success: false, 
        message: 'Card verification failed',
        error: verifyResponse.data.message 
      });
    }

    const transactionData = verifyResponse.data.data;
    const authorizationData = transactionData.authorization;

    // Check if this is a card verification transaction
    if (transactionData.metadata.purpose !== 'card_verification') {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid verification transaction' 
      });
    }

    // Create card record
    const card = await Card.createFromPaystackAuth(userId, authorizationData);
    
    // Complete verification
    await card.completeVerification();

    // Refund the verification amount to user's wallet
    const user = await User.findById(userId);
    user.balance += (transactionData.amount / 100); // Convert from kobo
    await user.save();

    // Create refund transaction record
    const transaction = new Transaction({
      userId,
      type: 'deposit',
      amount: transactionData.amount / 100,
      description: 'Card verification refund',
      reference: `REFUND_${reference}`,
      category: 'transfer',
      subcategory: 'card_verification_refund',
      paymentMethod: 'paystack',
      balanceAfter: user.balance,
    });

    await transaction.save();

    res.json({
      success: true,
      message: 'Card added and verified successfully',
      data: {
        card: {
          id: card._id,
          maskedPan: card.maskedPan,
          brand: card.brand,
          bank: card.bank,
          isDefault: card.isDefault,
          isVerified: card.isVerified
        },
        refundAmount: transactionData.amount / 100
      }
    });

  } catch (error) {
    console.error('Error verifying card:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/cards/user - Get user's cards
router.get('/user', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    const cards = await Card.find({ userId, isActive: true })
      .sort({ isDefault: -1, createdAt: -1 })
      .select('-authorizationCode -cardToken -signature'); // Exclude sensitive data

    const cardsWithStatus = cards.map(card => ({
      ...card.toObject(),
      maskedPan: card.maskedPan,
      expiryDisplay: card.expiryDisplay,
      isExpired: card.isExpired,
      canBeUsed: card.canBeUsed()
    }));

    res.json({
      success: true,
      data: {
        cards: cardsWithStatus,
        summary: {
          totalCards: cards.length,
          verifiedCards: cards.filter(c => c.isVerified).length,
          defaultCard: cards.find(c => c.isDefault) || null,
          expiredCards: cards.filter(c => c.isExpired).length
        }
      }
    });

  } catch (error) {
    console.error('Error fetching cards:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// PUT /api/cards/:id/set-default - Set card as default
router.put('/:id/set-default', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const card = await Card.findOne({ _id: id, userId });
    if (!card) {
      return res.status(404).json({ 
        success: false, 
        message: 'Card not found' 
      });
    }

    if (!card.canBeUsed()) {
      return res.status(400).json({ 
        success: false, 
        message: 'Card cannot be set as default. Please ensure it is verified and not blocked.' 
      });
    }

    await card.setAsDefault();

    res.json({
      success: true,
      message: 'Card set as default successfully',
      data: {
        cardId: card._id,
        maskedPan: card.maskedPan
      }
    });

  } catch (error) {
    console.error('Error setting default card:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// DELETE /api/cards/:id - Remove/deactivate card
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const card = await Card.findOne({ _id: id, userId });
    if (!card) {
      return res.status(404).json({ 
        success: false, 
        message: 'Card not found' 
      });
    }

    // Don't actually delete, just deactivate
    card.isActive = false;
    card.isDefault = false;
    await card.save();

    // If this was the default card, set another card as default
    if (card.isDefault) {
      const nextCard = await Card.findOne({ 
        userId, 
        isActive: true, 
        isVerified: true,
        _id: { $ne: card._id }
      });
      
      if (nextCard) {
        await nextCard.setAsDefault();
      }
    }

    res.json({
      success: true,
      message: 'Card removed successfully'
    });

  } catch (error) {
    console.error('Error removing card:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/cards/charge - Charge a specific card
router.post('/charge', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { cardId, amount, description, metadata } = req.body;

    if (!cardId || !amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Card ID and valid amount are required' 
      });
    }

    const card = await Card.findOne({ _id: cardId, userId });
    if (!card) {
      return res.status(404).json({ 
        success: false, 
        message: 'Card not found' 
      });
    }

    if (!card.canBeUsed()) {
      return res.status(400).json({ 
        success: false, 
        message: 'Card cannot be used. Please check if it is verified and not blocked.' 
      });
    }

    // Charge card via Paystack
    const chargeResponse = await axios.post(`${PAYSTACK_BASE_URL}/transaction/charge_authorization`, {
      authorization_code: card.authorizationCode,
      email: req.user.email,
      amount: amount * 100, // Convert to kobo
      metadata: {
        userId,
        cardId,
        description: description || 'Card charge',
        ...metadata
      },
      currency: 'NGN',
    }, {
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!chargeResponse.data.status) {
      // Record failure
      await card.recordFailure(chargeResponse.data.message);
      
      return res.status(400).json({ 
        success: false, 
        message: 'Card charge failed',
        error: chargeResponse.data.message 
      });
    }

    // Record successful transaction
    await card.recordTransaction(amount);

    // Update user balance
    const user = await User.findById(userId);
    user.balance += amount;
    await user.save();

    // Create transaction record
    const transaction = new Transaction({
      userId,
      type: 'deposit',
      amount,
      description: description || 'Card charge',
      reference: chargeResponse.data.data.reference,
      category: 'transfer',
      subcategory: 'card_charge',
      paymentMethod: 'card',
      balanceAfter: user.balance,
    });

    await transaction.save();

    res.json({
      success: true,
      message: 'Card charged successfully',
      data: {
        amount,
        reference: chargeResponse.data.data.reference,
        newBalance: user.balance
      }
    });

  } catch (error) {
    console.error('Error charging card:', error);
    
    // Record failure if card was found
    if (req.body.cardId) {
      try {
        const card = await Card.findById(req.body.cardId);
        if (card) {
          await card.recordFailure(error.message);
        }
      } catch (recordError) {
        console.error('Error recording card failure:', recordError);
      }
    }

    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

module.exports = router;
