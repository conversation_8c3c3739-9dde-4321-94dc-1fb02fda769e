import { Button } from "@/components/ui/button";
import { FintechCard } from "@/components/ui/fintech-card";
import { FloatingLabelInput } from "@/components/ui/floating-label-input";
import { useAuth } from '@/hooks/use-auth';
import { useAuthApi } from '@/hooks/use-auth-api';
import { validateEmail, validatePassword } from '@/utils/form-validation';
import { Fingerprint, Lock } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const Login = () => {
  const navigate = useNavigate();
  const { signIn } = useAuth();
  const { login, isLoading } = useAuthApi();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState<{email?: string; password?: string}>({});

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      navigate('/dashboard');
    }
  }, [navigate]);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);

    if (emailError || passwordError) {
      setErrors({
        email: emailError || undefined,
        password: passwordError || undefined,
      });
      return;
    }

    setErrors({});

    try {
      const result = await login({ email, password });
      if (result.data) {
        // Login successful, navigate to dashboard
        navigate('/dashboard');
      }
    } catch (error: any) {
      console.error("Login error:", error);
    }
  };

  const handleCreateAccount = () => {
    navigate('/signup');
  };

  return (
    <div className="min-h-screen fintech-gradient flex items-center justify-center mobile-safe-area">
      <div className="w-full max-w-md lg:max-w-lg xl:max-w-xl">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="h-16 w-16 rounded-full overflow-hidden ring-2 ring-primary/20 bg-white/10 backdrop-blur-sm">
                <img 
                  src="/lovable-uploads/532686f6-d477-4fff-934c-df00f9c4a11d.png" 
                  alt="Better Interest Logo" 
                  className="h-full w-full object-contain p-2"
                />
              </div>
            </div>
          
          <div className="flex items-center justify-center gap-2 mb-6">
            <Fingerprint className="h-5 w-5 text-primary" />
            <span className="text-white/90 text-sm">Secure • Fast • Reliable</span>
          </div>
        </div>
        
        <FintechCard variant="glassmorphic" className="responsive-card animate-fade-in">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold mb-2">Welcome Back</h2>
            <p className="text-muted-foreground">
              Sign in to continue to your dashboard
            </p>
          </div>
          
          <form onSubmit={handleEmailLogin} className="space-y-6">
            <div>
              <FloatingLabelInput
                id="email"
                type="email"
                label="Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
              )}
            </div>

            <div>
              <FloatingLabelInput
                id="password"
                type="password"
                label="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              {errors.password && (
                <p className="text-red-500 text-sm mt-1">{errors.password}</p>
              )}
            </div>
            
            <Button
              type="submit"
              className="mobile-button w-full"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">⟳</span>
                  Signing in...
                </>
              ) : (
                <>
                  <Lock className="h-4 w-4" />
                  Sign In
                </>
              )}
            </Button>
          </form>
          
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              Don't have an account?{" "}
              <button
                onClick={handleCreateAccount}
                className="text-primary hover:underline font-medium"
              >
                Create Account
              </button>
            </p>
          </div>
          
          <div className="mt-4 pt-4 border-t border-border">
            <p className="text-xs text-muted-foreground text-center">
              Protected by advanced encryption and secure authentication
            </p>
          </div>
        </FintechCard>
      </div>
    </div>
  );
};

export default Login;
