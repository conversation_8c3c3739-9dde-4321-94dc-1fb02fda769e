require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');

// Global connection cache for serverless
let cached = global.mongoose;
if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function connectToDatabase() {
  if (cached.conn) return cached.conn;
  if (!cached.promise) {
    cached.promise = mongoose.connect(process.env.MONGO_URI, {
      // Add options if needed
    }).then((mongoose) => {
      return mongoose;
    });
  }
  cached.conn = await cached.promise;
  return cached.conn;
}
const authRoutes = require('./routes/userAuth');
const savingsRoutes = require('./routes/savingsRoutes');
const kycRoutes = require('./routes/kycRoutes');
const dojahKycRoutes = require('./routes/dojahKyc');
const transactionRoutes = require('./routes/transactionRoutes');
const profitRoutes = require('./routes/profitRoutes');
const accountsRoutes = require('./routes/accountsRoutes'); // Import accounts routes
const withdrawAccountsRoutes = require('./routes/withdrawAccountsRoutes'); // Import withdraw accounts routes
const depositRoutes = require('./routes/depositRoutes'); // Import deposit routes
const userInfoRoutes = require('./routes/userInfo');
const globalSettingsRoutes = require('./routes/globalSettingsRoutes'); // Import global settings routes
const withdrawRoutes = require('./routes/withdrawRoutes'); // Import userInfo routes
const cors = require('cors');
const notificationRoutes = require('./routes/notificationRoutes'); // Import notification routes

const statsRoutes = require('./routes/statsRoutes'); // Import stats routes
const targetSavingsRoutes = require('./routes/targetSavingsRoutes'); // Import target savings routes

const paystackRoutes = require('./routes/paystackRoutes');

const paystackDepositRoutes = require('./routes/paystackDepositRoutes');

const groupSavingsRoutes = require('./routes/groupSavingsRoutes'); // Import group savings routes
const rotationalGroupSavingsRoutes = require('./routes/rotationalGroupSavingsRoutes'); // Import rotational group savings routes

const paystackWebhookRoutes = require('./routes/paystackWebhook'); // Import Paystack webhook route
const errorHandler = require('./middleware/errorHandler'); // Import error handler middleware

// Import new admin routes
const adminAuditLogsRoutes = require('./routes/adminAuditLogs');
const adminSystemLogsRoutes = require('./routes/adminSystemLogs');
const adminImpersonationRoutes = require('./routes/adminImpersonation');
const adminProfileRoutes = require('./routes/adminProfile');
const adminSettingsHistoryRoutes = require('./routes/adminSettingsHistory');
const userBulkActionsRoutes = require('./routes/userBulkActions');
const adminInterestManagementRoutes = require('./routes/adminInterestManagement');
const adminFeesManagementRoutes = require('./routes/adminFeesManagement');
const withdrawalSettingsRoutes = require('./routes/settingsRoutes'); // Import withdrawal penalty settings routes

// Import new enhanced savings routes
const fixedDepositRoutes = require('./routes/fixedDepositRoutes');
const flexSavingsRoutes = require('./routes/flexSavingsRoutes');
const safeLockRoutes = require('./routes/safeLockRoutes');
const cardRoutes = require('./routes/cardRoutes');
const autoSaveRoutes = require('./routes/autoSaveRoutes');
const interestRoutes = require('./routes/interestRoutes');
const referralRoutes = require('./routes/referralRoutes');

const app = express();
console.log('Express app initialized (serverless mode)');

// Require the savings scheduler to enable automated savings plan deductions
require('./scheduler/savingsScheduler');


// CORS configuration - Enhanced for development
const allowedOrigins = [
  'https://asusuprojectv2-davids-projects-20c3fae0.vercel.app',
  'https://asusuprojectv2-davids-projects-20c3fae0.vercel.app/',
  'https://amacmfbsavings.kojapay.io',
  'https://amacmfbsavings.kojapay.io/',
  'http://localhost:3000',
  'http://localhost:3000/',
  'http://localhost:8080',
  'http://localhost:8080/',
  'http://127.0.0.1:3000',
  'http://127.0.0.1:8080',
  process.env.FRONTEND_URL,
  process.env.CORS_ORIGIN,
];

const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl, Postman, etc.)
    if (!origin) return callback(null, true);

    // In development, be more permissive
    if (process.env.NODE_ENV === 'development') {
      // Allow localhost with any port
      if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
        return callback(null, true);
      }
    }

    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    } else {
      console.log(`CORS blocked origin: ${origin}`);
      return callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers'
  ],
  credentials: true,
  optionsSuccessStatus: 200, // For legacy browser support
  preflightContinue: false,
};
console.log('CORS allowed origins:', allowedOrigins);

// Place CORS middleware at the very top before any other middleware/routes
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] Incoming request: ${req.method} ${req.url} from origin: ${req.headers.origin || 'no-origin'}`);
  next();
});

// Enhanced CORS middleware for development
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');

    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  });
} else {
  app.use(cors(corsOptions));
}

app.use(express.json());

// Add a simple test endpoint for CORS verification
app.get('/api/test', (req, res) => {
  res.json({
    message: 'CORS is working! Backend is accessible.',
    timestamp: new Date().toISOString(),
    origin: req.headers.origin || 'no-origin',
    method: req.method
  });
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'KojaPay Backend is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Register admin dashboard route (ensure this is after express.json and before other routes)
const adminDashboardRoutes = require('./routes/adminDashboard');
app.use('/api/admin', adminDashboardRoutes);

// Register new admin routes
app.use('/api/admin/audit-logs', adminAuditLogsRoutes);
app.use('/api/admin/system-logs', adminSystemLogsRoutes);
app.use('/api/admin/impersonation', adminImpersonationRoutes);
app.use('/api/admin/profile', adminProfileRoutes);
app.use('/api/admin/settings/history', adminSettingsHistoryRoutes);
app.use('/api/users', userBulkActionsRoutes);
app.use('/api/admin/interest-management', adminInterestManagementRoutes);
app.use('/api/admin/fees-management', adminFeesManagementRoutes);

app.use('/api/auth', authRoutes);
app.use('/api/users', authRoutes); // <-- Add this line to expose /api/users/recent
app.use('/api/savings', savingsRoutes);
app.use('/api/kyc', kycRoutes);
app.use('/api/kyc', dojahKycRoutes); // /api/kyc/verify-nin
app.use('/api/transactions', transactionRoutes);
app.use('/api/profits', profitRoutes);
app.use('/api/accounts', accountsRoutes); // Add accounts routes
app.use('/api/withdraw-accounts', withdrawAccountsRoutes); // Add withdraw accounts routes
app.use('/api/deposits', depositRoutes); // Add deposit routes

// Mount global settings routes at /api/settings
app.use('/api/settings', globalSettingsRoutes);
app.use('/api/withdrawalset', withdrawalSettingsRoutes); // Add withdrawal penalty settings routes

app.use('/api/userinfo', userInfoRoutes); // Add userInfo routes
app.use('/api/user/profile', require('./routes/userProfile'));
app.use('/api/withdraw', withdrawRoutes); // Add withdraw routes

app.use('/api/paystack', paystackRoutes); // Paystack endpoints
app.use('/api/paystack/deposit', paystackDepositRoutes); // Paystack deposit endpoints
app.use('/api', paystackWebhookRoutes); // Paystack webhook endpoint
app.use('/api/notifications', notificationRoutes); // Add notification routes
app.use('/api', statsRoutes); // Add stats routes

app.use('/api/group-savings', groupSavingsRoutes); // Add group savings routes
app.use('/api/rotational-group-savings', rotationalGroupSavingsRoutes); // Add rotational group savings routes
app.use('/api/target-savings', targetSavingsRoutes); // Add target savings routes

// Add new enhanced savings routes
app.use('/api/fixed-deposit', fixedDepositRoutes); // Fixed deposit routes
app.use('/api/flex-savings', flexSavingsRoutes); // Flexible savings routes
app.use('/api/safelock', safeLockRoutes); // SafeLock routes
app.use('/api/cards', cardRoutes); // Card management routes
app.use('/api/autosave', autoSaveRoutes); // AutoSave routes
app.use('/api/interest', interestRoutes); // Interest calculation routes
app.use('/api/referral', referralRoutes); // Referral system routes
app.use(errorHandler); // Use the error handler middleware


// For Vercel: connect on every request (but use cache)
connectToDatabase().then(() => {
  console.log('MongoDB connected (Vercel serverless)');
}).catch(err => console.log('MongoDB connection error:', err));

// Local development server
if (require.main === module) {
  const PORT = process.env.PORT || 3001;
  app.listen(PORT, () => {
    console.log(`🚀 KojaPay Backend Server running on port ${PORT}`);
    console.log(`📡 API Base URL: http://localhost:${PORT}/api`);
    console.log(`🔗 Test Endpoint: http://localhost:${PORT}/api/test`);
    console.log(`💚 Health Check: http://localhost:${PORT}/api/health`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  });
} else {
  console.log('Backend exported for Vercel serverless API.');
}

// Export the app for Vercel serverless
module.exports = app;
