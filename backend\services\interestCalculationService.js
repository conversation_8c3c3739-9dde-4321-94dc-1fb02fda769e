const User = require('../models/user');
const Transaction = require('../models/transaction');
const FlexSavings = require('../models/flexSavings');
const FixedDeposit = require('../models/fixedDeposit');
const SafeLock = require('../models/safeLock');
const SavingsPlan = require('../models/savingsPlan');

class InterestCalculationService {
  constructor() {
    this.calculationDate = new Date();
    this.calculationResults = {
      flexSavings: { processed: 0, totalInterest: 0, errors: [] },
      fixedDeposits: { processed: 0, totalInterest: 0, errors: [] },
      safeLocks: { processed: 0, totalInterest: 0, errors: [] },
      targetSavings: { processed: 0, totalInterest: 0, errors: [] },
      summary: { totalProcessed: 0, totalInterest: 0, totalErrors: 0 }
    };
  }

  // Main method to process all interest calculations
  async processAllInterest() {
    console.log(`[InterestService] Starting daily interest calculation for ${this.calculationDate.toDateString()}`);
    
    try {
      // Process different savings products in parallel
      await Promise.all([
        this.processFlexSavingsInterest(),
        this.processFixedDepositInterest(),
        this.processSafeLockInterest(),
        this.processTargetSavingsInterest()
      ]);

      // Calculate summary
      this.calculateSummary();

      // Log results
      this.logResults();

      // Send admin notification if there are errors
      if (this.calculationResults.summary.totalErrors > 0) {
        await this.notifyAdminOfErrors();
      }

      return this.calculationResults;

    } catch (error) {
      console.error('[InterestService] Error in processAllInterest:', error);
      throw error;
    }
  }

  // Process Flex Savings interest
  async processFlexSavingsInterest() {
    try {
      console.log('[InterestService] Processing Flex Savings interest...');
      
      const flexAccounts = await FlexSavings.getAccountsForInterestCalculation();
      
      for (const account of flexAccounts) {
        try {
          const interest = account.addDailyInterest();
          
          if (interest > 0) {
            await account.save();
            
            // Create interest transaction
            await this.createInterestTransaction(
              account.userId,
              interest,
              'Flex Savings daily interest',
              `FLEX_INT_${Date.now()}_${account._id}`,
              'flex_savings'
            );

            // Update user's total earnings
            await this.updateUserEarnings(account.userId, interest);

            this.calculationResults.flexSavings.processed++;
            this.calculationResults.flexSavings.totalInterest += interest;
          }
        } catch (error) {
          console.error(`[InterestService] Error processing flex account ${account._id}:`, error);
          this.calculationResults.flexSavings.errors.push({
            accountId: account._id,
            userId: account.userId,
            error: error.message
          });
        }
      }

      console.log(`[InterestService] Processed ${this.calculationResults.flexSavings.processed} Flex Savings accounts`);
      
    } catch (error) {
      console.error('[InterestService] Error in processFlexSavingsInterest:', error);
      throw error;
    }
  }

  // Process Fixed Deposit interest
  async processFixedDepositInterest() {
    try {
      console.log('[InterestService] Processing Fixed Deposit interest...');
      
      const activeDeposits = await FixedDeposit.find({ status: 'active' });
      
      for (const deposit of activeDeposits) {
        try {
          const previousInterest = deposit.accruedInterest;
          const currentInterest = deposit.calculateAccruedInterest();
          const dailyInterest = currentInterest - previousInterest;
          
          if (dailyInterest > 0) {
            // Update accrued interest
            deposit.accruedInterest = currentInterest;
            
            // Add to daily interest log
            deposit.dailyInterestLog.push({
              date: new Date(),
              interestEarned: dailyInterest,
              cumulativeInterest: currentInterest
            });
            
            // Keep only last 365 days of logs
            if (deposit.dailyInterestLog.length > 365) {
              deposit.dailyInterestLog = deposit.dailyInterestLog.slice(-365);
            }
            
            await deposit.save();

            this.calculationResults.fixedDeposits.processed++;
            this.calculationResults.fixedDeposits.totalInterest += dailyInterest;
          }

          // Check if deposit has matured
          if (deposit.hasMatured()) {
            await deposit.processMaturity();
            
            // Create maturity transaction
            await this.createInterestTransaction(
              deposit.userId,
              deposit.accruedInterest,
              `Fixed Deposit maturity - ${deposit.duration} days`,
              `${deposit.reference}_MATURITY`,
              'fixed_deposit'
            );

            // Update user balances
            const user = await User.findById(deposit.userId);
            user.balance += deposit.amount + deposit.accruedInterest;
            user.savingsBalance -= deposit.amount;
            user.totalEarnings += deposit.accruedInterest;
            await user.save();
          }
          
        } catch (error) {
          console.error(`[InterestService] Error processing fixed deposit ${deposit._id}:`, error);
          this.calculationResults.fixedDeposits.errors.push({
            depositId: deposit._id,
            userId: deposit.userId,
            error: error.message
          });
        }
      }

      console.log(`[InterestService] Processed ${this.calculationResults.fixedDeposits.processed} Fixed Deposits`);
      
    } catch (error) {
      console.error('[InterestService] Error in processFixedDepositInterest:', error);
      throw error;
    }
  }

  // Process SafeLock interest
  async processSafeLockInterest() {
    try {
      console.log('[InterestService] Processing SafeLock interest...');
      
      const activeSafeLocks = await SafeLock.find({ status: 'active' });
      
      for (const safeLock of activeSafeLocks) {
        try {
          const previousInterest = safeLock.accruedInterest;
          const currentInterest = safeLock.calculateAccruedInterest();
          const dailyInterest = currentInterest - previousInterest;
          
          if (dailyInterest > 0) {
            safeLock.accruedInterest = currentInterest;
            await safeLock.save();

            this.calculationResults.safeLocks.processed++;
            this.calculationResults.safeLocks.totalInterest += dailyInterest;
          }

          // Check if SafeLock has matured
          if (safeLock.hasMatured()) {
            await safeLock.processMaturity();
            
            // Create maturity transaction
            const bonus = safeLock.amount * (safeLock.maturityBonus / 100);
            const totalAmount = safeLock.amount + safeLock.accruedInterest + bonus;
            
            await this.createInterestTransaction(
              safeLock.userId,
              safeLock.accruedInterest + bonus,
              `SafeLock maturity with ${safeLock.maturityBonus}% bonus`,
              `${safeLock.reference}_MATURITY`,
              'safelock'
            );

            // Update user balances
            const user = await User.findById(safeLock.userId);
            user.balance += totalAmount;
            user.savingsBalance -= safeLock.amount;
            user.totalEarnings += safeLock.accruedInterest + bonus;
            await user.save();
          }
          
        } catch (error) {
          console.error(`[InterestService] Error processing SafeLock ${safeLock._id}:`, error);
          this.calculationResults.safeLocks.errors.push({
            safeLockId: safeLock._id,
            userId: safeLock.userId,
            error: error.message
          });
        }
      }

      console.log(`[InterestService] Processed ${this.calculationResults.safeLocks.processed} SafeLocks`);
      
    } catch (error) {
      console.error('[InterestService] Error in processSafeLockInterest:', error);
      throw error;
    }
  }

  // Process Target Savings interest
  async processTargetSavingsInterest() {
    try {
      console.log('[InterestService] Processing Target Savings interest...');
      
      const activePlans = await SavingsPlan.find({ 
        savedAmount: { $gt: 0 },
        // Add any other conditions for active plans
      });
      
      for (const plan of activePlans) {
        try {
          // Calculate daily interest (assuming 10% annual rate for target savings)
          const annualRate = 0.10; // 10%
          const dailyRate = annualRate / 365;
          const dailyInterest = plan.savedAmount * dailyRate;
          
          if (dailyInterest > 0) {
            plan.savedAmount += dailyInterest;
            plan.lastInterestApplied = new Date();
            await plan.save();

            // Create interest transaction
            await this.createInterestTransaction(
              plan.userId,
              dailyInterest,
              `Target Savings interest - ${plan.title}`,
              `TARGET_INT_${Date.now()}_${plan._id}`,
              'target_savings'
            );

            // Update user's total earnings
            await this.updateUserEarnings(plan.userId, dailyInterest);

            this.calculationResults.targetSavings.processed++;
            this.calculationResults.targetSavings.totalInterest += dailyInterest;
          }
          
        } catch (error) {
          console.error(`[InterestService] Error processing target savings ${plan._id}:`, error);
          this.calculationResults.targetSavings.errors.push({
            planId: plan._id,
            userId: plan.userId,
            error: error.message
          });
        }
      }

      console.log(`[InterestService] Processed ${this.calculationResults.targetSavings.processed} Target Savings plans`);
      
    } catch (error) {
      console.error('[InterestService] Error in processTargetSavingsInterest:', error);
      throw error;
    }
  }

  // Helper method to create interest transaction
  async createInterestTransaction(userId, amount, description, reference, subcategory) {
    try {
      const transaction = new Transaction({
        userId,
        type: 'interest',
        amount,
        description,
        reference,
        category: 'savings',
        subcategory,
        paymentMethod: 'internal',
        balanceAfter: 0, // Will be updated by the calling function if needed
      });

      await transaction.save();
      return transaction;
    } catch (error) {
      console.error('[InterestService] Error creating interest transaction:', error);
      throw error;
    }
  }

  // Helper method to update user's total earnings
  async updateUserEarnings(userId, amount) {
    try {
      await User.findByIdAndUpdate(userId, {
        $inc: { totalEarnings: amount }
      });
    } catch (error) {
      console.error('[InterestService] Error updating user earnings:', error);
      throw error;
    }
  }

  // Calculate summary statistics
  calculateSummary() {
    const results = this.calculationResults;
    
    results.summary.totalProcessed = 
      results.flexSavings.processed + 
      results.fixedDeposits.processed + 
      results.safeLocks.processed + 
      results.targetSavings.processed;
      
    results.summary.totalInterest = 
      results.flexSavings.totalInterest + 
      results.fixedDeposits.totalInterest + 
      results.safeLocks.totalInterest + 
      results.targetSavings.totalInterest;
      
    results.summary.totalErrors = 
      results.flexSavings.errors.length + 
      results.fixedDeposits.errors.length + 
      results.safeLocks.errors.length + 
      results.targetSavings.errors.length;
  }

  // Log calculation results
  logResults() {
    const summary = this.calculationResults.summary;
    console.log(`[InterestService] Daily interest calculation completed:`);
    console.log(`- Total accounts processed: ${summary.totalProcessed}`);
    console.log(`- Total interest distributed: ₦${summary.totalInterest.toFixed(2)}`);
    console.log(`- Total errors: ${summary.totalErrors}`);
  }

  // Notify admin of errors (placeholder)
  async notifyAdminOfErrors() {
    // This would integrate with your notification system
    console.log('[InterestService] Notifying admin of calculation errors...');
    // Implementation would depend on your notification system
  }
}

module.exports = InterestCalculationService;
