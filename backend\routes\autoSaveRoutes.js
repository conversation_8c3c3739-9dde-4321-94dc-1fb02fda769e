const express = require('express');
const router = express.Router();
const AutoSave = require('../models/autoSave');
const Card = require('../models/card');
const User = require('../models/user');
const Transaction = require('../models/transaction');
const { authenticateToken } = require('../middleware/authMiddleware');

// POST /api/autosave/setup - Setup new AutoSave
router.post('/setup', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { 
      cardId, 
      frequency, 
      amount, 
      targetType = 'flex_savings',
      targetPlanId,
      preferredTime = '09:00',
      avoidWeekends = true 
    } = req.body;

    // Validation
    if (!cardId || !frequency || !amount) {
      return res.status(400).json({ 
        success: false, 
        message: 'Card ID, frequency, and amount are required' 
      });
    }

    if (amount < 100) {
      return res.status(400).json({ 
        success: false, 
        message: 'Minimum AutoSave amount is ₦100' 
      });
    }

    if (!['daily', 'weekly', 'monthly'].includes(frequency)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Frequency must be daily, weekly, or monthly' 
      });
    }

    // Verify card belongs to user and can be used
    const card = await Card.findOne({ _id: cardId, userId });
    if (!card) {
      return res.status(404).json({ 
        success: false, 
        message: 'Card not found' 
      });
    }

    if (!card.canBeUsed()) {
      return res.status(400).json({ 
        success: false, 
        message: 'Card cannot be used for AutoSave. Please ensure it is verified and not blocked.' 
      });
    }

    // Check if user already has an active AutoSave with this card
    const existingAutoSave = await AutoSave.findOne({ 
      userId, 
      cardId, 
      isActive: true 
    });

    if (existingAutoSave) {
      return res.status(400).json({ 
        success: false, 
        message: 'You already have an active AutoSave with this card' 
      });
    }

    // Create AutoSave
    const autoSave = new AutoSave({
      userId,
      cardId,
      frequency,
      amount,
      targetType,
      targetPlanId,
      preferredTime,
      avoidWeekends,
    });

    await autoSave.save();

    // Populate card details for response
    await autoSave.populate('cardId', 'maskedPan brand bank');

    res.status(201).json({
      success: true,
      message: 'AutoSave setup successfully',
      data: {
        autoSave,
        nextDebitDate: autoSave.nextDebitDate,
        estimatedMonthlyAmount: autoSave.frequency === 'daily' ? amount * 30 : 
                               autoSave.frequency === 'weekly' ? amount * 4 : amount
      }
    });

  } catch (error) {
    console.error('Error setting up AutoSave:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/autosave/user - Get user's AutoSaves
router.get('/user', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    const autoSaves = await AutoSave.getUserAutoSaves(userId);

    // Calculate summary statistics
    const summary = {
      totalAutoSaves: autoSaves.length,
      activeAutoSaves: autoSaves.filter(as => as.isActive && !as.isPaused).length,
      pausedAutoSaves: autoSaves.filter(as => as.isPaused).length,
      totalAmountSaved: autoSaves.reduce((sum, as) => sum + as.totalAmount, 0),
      totalDebits: autoSaves.reduce((sum, as) => sum + as.totalDebits, 0),
      nextDebitDate: autoSaves
        .filter(as => as.isActive && !as.isPaused)
        .sort((a, b) => a.nextDebitDate - b.nextDebitDate)[0]?.nextDebitDate || null
    };

    res.json({
      success: true,
      data: {
        autoSaves,
        summary
      }
    });

  } catch (error) {
    console.error('Error fetching AutoSaves:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// PUT /api/autosave/:id/toggle - Toggle AutoSave active status
router.put('/:id/toggle', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { action } = req.body; // 'pause', 'resume', 'deactivate'

    const autoSave = await AutoSave.findOne({ _id: id, userId });
    if (!autoSave) {
      return res.status(404).json({ 
        success: false, 
        message: 'AutoSave not found' 
      });
    }

    let message = '';
    
    switch (action) {
      case 'pause':
        const { duration = 7 } = req.body;
        await autoSave.pause(duration);
        message = `AutoSave paused for ${duration} days`;
        break;
        
      case 'resume':
        await autoSave.resume();
        message = 'AutoSave resumed successfully';
        break;
        
      case 'deactivate':
        autoSave.isActive = false;
        await autoSave.save();
        message = 'AutoSave deactivated';
        break;
        
      default:
        return res.status(400).json({ 
          success: false, 
          message: 'Invalid action. Use pause, resume, or deactivate' 
        });
    }

    res.json({
      success: true,
      message,
      data: {
        autoSave: {
          id: autoSave._id,
          isActive: autoSave.isActive,
          isPaused: autoSave.isPaused,
          pausedUntil: autoSave.pausedUntil,
          nextDebitDate: autoSave.nextDebitDate
        }
      }
    });

  } catch (error) {
    console.error('Error toggling AutoSave:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// PUT /api/autosave/:id/update - Update AutoSave settings
router.put('/:id/update', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { amount, frequency, preferredTime, avoidWeekends } = req.body;

    const autoSave = await AutoSave.findOne({ _id: id, userId });
    if (!autoSave) {
      return res.status(404).json({ 
        success: false, 
        message: 'AutoSave not found' 
      });
    }

    // Update amount if provided
    if (amount !== undefined) {
      await autoSave.updateAmount(amount);
    }

    // Update frequency if provided
    if (frequency !== undefined) {
      await autoSave.updateFrequency(frequency);
    }

    // Update other settings
    if (preferredTime !== undefined) {
      autoSave.preferredTime = preferredTime;
    }
    
    if (avoidWeekends !== undefined) {
      autoSave.avoidWeekends = avoidWeekends;
    }

    await autoSave.save();

    res.json({
      success: true,
      message: 'AutoSave updated successfully',
      data: {
        autoSave: {
          id: autoSave._id,
          amount: autoSave.amount,
          frequency: autoSave.frequency,
          preferredTime: autoSave.preferredTime,
          avoidWeekends: autoSave.avoidWeekends,
          nextDebitDate: autoSave.nextDebitDate
        }
      }
    });

  } catch (error) {
    console.error('Error updating AutoSave:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/autosave/process-debits - Process all due AutoSave debits (admin/cron)
router.post('/process-debits', authenticateToken, async (req, res) => {
  try {
    // This endpoint should be protected and only accessible by admin or cron jobs
    const user = req.user;
    if (user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied. Admin privileges required.' 
      });
    }

    const autoSaves = await AutoSave.getAutoSavesForProcessing();
    const results = {
      processed: 0,
      successful: 0,
      failed: 0,
      details: []
    };

    for (const autoSave of autoSaves) {
      results.processed++;
      
      try {
        const result = await autoSave.processDebit();
        
        if (result.success) {
          results.successful++;
          results.details.push({
            autoSaveId: autoSave._id,
            userId: autoSave.userId,
            amount: autoSave.amount,
            status: 'success'
          });
        } else {
          results.failed++;
          results.details.push({
            autoSaveId: autoSave._id,
            userId: autoSave.userId,
            amount: autoSave.amount,
            status: 'failed',
            reason: result.reason
          });
        }
      } catch (error) {
        results.failed++;
        results.details.push({
          autoSaveId: autoSave._id,
          userId: autoSave.userId,
          amount: autoSave.amount,
          status: 'error',
          reason: error.message
        });
      }
    }

    res.json({
      success: true,
      message: 'AutoSave debits processed',
      data: results
    });

  } catch (error) {
    console.error('Error processing AutoSave debits:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/autosave/:id/history - Get AutoSave transaction history
router.get('/:id/history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { limit = 20, page = 1 } = req.query;

    const autoSave = await AutoSave.findOne({ _id: id, userId });
    if (!autoSave) {
      return res.status(404).json({ 
        success: false, 
        message: 'AutoSave not found' 
      });
    }

    // Get transactions related to this AutoSave
    const transactions = await Transaction.find({
      userId,
      reference: { $regex: autoSave.reference }
    })
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip((parseInt(page) - 1) * parseInt(limit));

    const totalTransactions = await Transaction.countDocuments({
      userId,
      reference: { $regex: autoSave.reference }
    });

    res.json({
      success: true,
      data: {
        autoSave: {
          id: autoSave._id,
          reference: autoSave.reference,
          totalDebits: autoSave.totalDebits,
          totalAmount: autoSave.totalAmount
        },
        transactions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalTransactions / parseInt(limit)),
          totalTransactions,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching AutoSave history:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

module.exports = router;
