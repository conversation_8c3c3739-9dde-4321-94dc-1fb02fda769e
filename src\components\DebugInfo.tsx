import { API_CONFIG } from '@/config/env';

export function DebugInfo() {
  const testApiConnection = async () => {
    console.log('🔍 Testing API connection...');
    console.log('API_CONFIG:', API_CONFIG);
    
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/health`);
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ API Response:', data);
        alert(`✅ API Connected!\nStatus: ${data.status}\nMessage: ${data.message}`);
      } else {
        console.error('❌ API Error:', response.status, response.statusText);
        alert(`❌ API Error: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('❌ Network Error:', error);
      alert(`❌ Network Error: ${error}`);
    }
  };

  return (
    <div className="fixed top-4 left-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <h3 className="text-sm font-semibold text-gray-800 mb-2">Debug Info</h3>
      
      <div className="text-xs space-y-1 mb-3">
        <div>
          <span className="font-medium">API URL:</span>
          <div className="text-gray-600 break-all">{API_CONFIG.BASE_URL}</div>
        </div>
        <div>
          <span className="font-medium">Backend URL:</span>
          <div className="text-gray-600 break-all">{API_CONFIG.BACKEND_URL}</div>
        </div>
        <div>
          <span className="font-medium">Environment:</span>
          <div className="text-gray-600">
            NEXT_PUBLIC_API_URL: {import.meta.env.NEXT_PUBLIC_API_URL || 'undefined'}
          </div>
        </div>
      </div>
      
      <button
        onClick={testApiConnection}
        className="w-full text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
      >
        Test API Connection
      </button>
    </div>
  );
}
