@echo off
echo 🚀 Starting KojaPay Frontend...
echo.
echo Backend is running on: http://localhost:3001
echo Frontend will start on: http://localhost:3000
echo.

REM Try different methods to start the frontend
echo Attempting to start with npm run dev...
npm run dev

REM If that fails, try npx vite
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo npm run dev failed, trying npx vite...
    npx vite
)

REM If that fails, try vite directly
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo npx vite failed, trying vite directly...
    vite
)

pause
