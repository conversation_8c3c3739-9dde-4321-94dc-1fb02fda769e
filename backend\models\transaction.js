const mongoose = require('mongoose');

const TransactionSchema = new mongoose.Schema({
  date: {
    type: Date,
    default: Date.now,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    enum: ['deposit', 'withdrawal', 'withdrawal_refund', 'interest', 'withdrawal_penalty', 'transfer', 'bill_payment', 'loan_disbursement', 'loan_repayment', 'referral_bonus', 'investment', 'fixed_deposit', 'flex_savings', 'safelock', 'autosave'],
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  // Enhanced transaction categorization
  category: {
    type: String,
    enum: ['savings', 'investment', 'loan', 'bill_payment', 'transfer', 'referral', 'interest', 'penalty', 'bonus'],
    required: true,
  },
  subcategory: {
    type: String,
    default: null,
  },
  // Merchant and location info
  merchantName: {
    type: String,
    default: null,
  },
  location: {
    type: String,
    default: null,
  },
  // Payment method tracking
  paymentMethod: {
    type: String,
    enum: ['wallet', 'card', 'bank_transfer', 'paystack', 'internal'],
    default: 'internal',
  },
  // Fee tracking
  fees: {
    type: Number,
    default: 0,
  },
  // Currency handling
  exchangeRate: {
    type: Number,
    default: 1,
  },
  originalAmount: {
    type: Number,
    default: null,
  },
  originalCurrency: {
    type: String,
    default: 'NGN',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  balanceAfter: {
    type: Number,
    required: true,
  },
  reference: {
    type: String,
    required: true,
    unique: true,
  },
});

module.exports = mongoose.model('Transaction', TransactionSchema);
