const express = require('express');
const router = express.Router();
const SafeLock = require('../models/safeLock');
const User = require('../models/user');
const Transaction = require('../models/transaction');
const { authenticateToken } = require('../middleware/authMiddleware');

// POST /api/safelock/create - Create a new SafeLock
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { amount, lockDuration, canBreak } = req.body;

    // Validation
    if (!amount || !lockDuration) {
      return res.status(400).json({ 
        success: false, 
        message: 'Amount and lock duration are required' 
      });
    }

    if (amount < 5000) {
      return res.status(400).json({ 
        success: false, 
        message: 'Minimum SafeLock amount is ₦5,000' 
      });
    }

    if (lockDuration < 30 || lockDuration > 365) {
      return res.status(400).json({ 
        success: false, 
        message: 'Lock duration must be between 30 and 365 days' 
      });
    }

    // Check user balance
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    if (user.balance < amount) {
      return res.status(400).json({ 
        success: false, 
        message: 'Insufficient balance' 
      });
    }

    // Create SafeLock
    const safeLock = new SafeLock({
      userId,
      amount,
      lockDuration,
      canBreak: canBreak !== undefined ? canBreak : true,
    });

    await safeLock.save();

    // Deduct amount from user balance
    user.balance -= amount;
    user.savingsBalance += amount;
    await user.save();

    // Create transaction record
    const transaction = new Transaction({
      userId,
      type: 'safelock',
      amount: -amount,
      description: `SafeLock created - ${lockDuration} days at ${safeLock.interestRate}% (${safeLock.lockTier} tier)`,
      reference: safeLock.reference,
      category: 'savings',
      subcategory: 'safelock',
      paymentMethod: 'wallet',
      balanceAfter: user.balance,
    });

    await transaction.save();

    res.status(201).json({
      success: true,
      message: 'SafeLock created successfully',
      data: {
        safeLock,
        projectedReturns: {
          maturityValue: safeLock.totalMaturityValue,
          totalReturns: safeLock.totalMaturityValue - amount,
          interestRate: safeLock.interestRate,
          tier: safeLock.lockTier,
          maturityBonus: amount * (safeLock.maturityBonus / 100)
        }
      }
    });

  } catch (error) {
    console.error('Error creating SafeLock:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/safelock/user - Get user's SafeLocks
router.get('/user', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { status } = req.query;

    let query = { userId };
    if (status) {
      query.status = status;
    }

    const safeLocks = await SafeLock.find(query)
      .sort({ createdAt: -1 })
      .populate('userId', 'firstName lastName email');

    // Calculate current values for each SafeLock
    const safeLocksWithCurrentValues = safeLocks.map(safeLock => {
      const currentInterest = safeLock.calculateAccruedInterest();
      const currentValue = safeLock.amount + currentInterest;
      const daysRemaining = safeLock.hasMatured() ? 0 : Math.ceil((safeLock.maturityDate - new Date()) / (1000 * 60 * 60 * 24));
      
      return {
        ...safeLock.toObject(),
        currentInterest,
        currentValue,
        daysRemaining,
        progressPercentage: Math.min(100, ((safeLock.lockDuration - daysRemaining) / safeLock.lockDuration) * 100)
      };
    });

    // Calculate summary
    const summary = {
      totalSafeLocks: safeLocks.length,
      activeSafeLocks: safeLocks.filter(sl => sl.status === 'active').length,
      maturedSafeLocks: safeLocks.filter(sl => sl.status === 'matured').length,
      brokenSafeLocks: safeLocks.filter(sl => sl.status === 'broken').length,
      totalAmount: safeLocks.reduce((sum, sl) => sum + sl.amount, 0),
      totalCurrentValue: safeLocksWithCurrentValues.reduce((sum, sl) => sum + sl.currentValue, 0),
      totalProjectedValue: safeLocks.filter(sl => sl.status === 'active').reduce((sum, sl) => sum + sl.totalMaturityValue, 0)
    };

    res.json({
      success: true,
      data: {
        safeLocks: safeLocksWithCurrentValues,
        summary
      }
    });

  } catch (error) {
    console.error('Error fetching SafeLocks:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/safelock/break/:id - Emergency break SafeLock
router.post('/break/:id', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { reason, confirmBreak } = req.body;

    if (!confirmBreak) {
      return res.status(400).json({ 
        success: false, 
        message: 'Please confirm that you want to break this SafeLock' 
      });
    }

    const safeLock = await SafeLock.findOne({ _id: id, userId });
    if (!safeLock) {
      return res.status(404).json({ 
        success: false, 
        message: 'SafeLock not found' 
      });
    }

    if (safeLock.status !== 'active') {
      return res.status(400).json({ 
        success: false, 
        message: 'SafeLock is not active' 
      });
    }

    if (!safeLock.canBreak) {
      return res.status(400).json({ 
        success: false, 
        message: 'Emergency break is not allowed for this SafeLock' 
      });
    }

    // Calculate penalty and amount to receive
    const penalty = safeLock.calculateBreakPenalty();
    const amountReceived = await safeLock.processEmergencyBreak(reason);

    // Update user balance
    const user = await User.findById(userId);
    user.balance += amountReceived;
    user.savingsBalance -= safeLock.amount;
    await user.save();

    // Create transaction records
    const transactions = [];

    // Penalty transaction
    if (penalty > 0) {
      transactions.push(new Transaction({
        userId,
        type: 'withdrawal_penalty',
        amount: -penalty,
        description: `SafeLock emergency break penalty - ${safeLock.breakagePenalty}% of principal`,
        reference: `${safeLock.reference}_PENALTY`,
        category: 'penalty',
        subcategory: 'safelock_break',
        paymentMethod: 'internal',
        balanceAfter: user.balance - amountReceived,
      }));
    }

    // Amount received transaction
    if (amountReceived > 0) {
      transactions.push(new Transaction({
        userId,
        type: 'withdrawal',
        amount: amountReceived,
        description: `SafeLock emergency break - Amount received after penalty`,
        reference: `${safeLock.reference}_BREAK`,
        category: 'savings',
        subcategory: 'safelock_break',
        paymentMethod: 'internal',
        balanceAfter: user.balance,
      }));
    }

    await Transaction.insertMany(transactions);

    res.json({
      success: true,
      message: 'SafeLock broken successfully',
      data: {
        safeLock,
        breakdown: {
          originalAmount: safeLock.amount,
          penalty,
          amountReceived,
          penaltyPercentage: safeLock.breakagePenalty
        }
      }
    });

  } catch (error) {
    console.error('Error breaking SafeLock:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/safelock/calculate-returns - Calculate projected returns
router.get('/calculate-returns', authenticateToken, async (req, res) => {
  try {
    const { amount, duration } = req.query;

    if (!amount || !duration) {
      return res.status(400).json({ 
        success: false, 
        message: 'Amount and duration are required' 
      });
    }

    if (amount < 5000) {
      return res.status(400).json({ 
        success: false, 
        message: 'Minimum amount is ₦5,000' 
      });
    }

    const projectedReturns = SafeLock.calculateProjectedReturns(amount, duration);

    res.json({
      success: true,
      data: projectedReturns
    });

  } catch (error) {
    console.error('Error calculating returns:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/safelock/rates - Get interest rates and tiers
router.get('/rates', authenticateToken, async (req, res) => {
  try {
    const rates = SafeLock.getInterestRates();

    res.json({
      success: true,
      data: {
        rates,
        tiers: {
          bronze: { minAmount: 5000, minDuration: 30, description: 'Entry level SafeLock' },
          silver: { minAmount: 100000, minDuration: 90, description: 'Premium SafeLock with higher returns' },
          gold: { minAmount: 500000, minDuration: 180, description: 'Gold tier with excellent returns' },
          platinum: { minAmount: 1000000, minDuration: 365, description: 'Platinum tier with maximum returns' }
        },
        features: {
          emergencyBreak: 'Available with 50% penalty on principal',
          maturityBonus: '2% bonus on successful completion',
          compoundInterest: 'Daily compounding for maximum growth',
          noWithdrawals: 'Funds completely locked until maturity'
        }
      }
    });

  } catch (error) {
    console.error('Error fetching rates:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

module.exports = router;
