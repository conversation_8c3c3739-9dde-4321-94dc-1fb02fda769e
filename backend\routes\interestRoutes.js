const express = require('express');
const router = express.Router();
const InterestCalculationService = require('../services/interestCalculationService');
const Transaction = require('../models/transaction');
const User = require('../models/user');
const { authenticateToken } = require('../middleware/authMiddleware');

// POST /api/interest/calculate - Manually trigger interest calculation (admin only)
router.post('/calculate', authenticateToken, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied. Admin privileges required.' 
      });
    }

    const interestService = new InterestCalculationService();
    const results = await interestService.processAllInterest();

    res.json({
      success: true,
      message: 'Interest calculation completed',
      data: results
    });

  } catch (error) {
    console.error('Error calculating interest:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// POST /api/interest/process-daily - Process daily interest (cron job endpoint)
router.post('/process-daily', async (req, res) => {
  try {
    // This endpoint should be called by cron jobs
    // Add IP whitelist or API key authentication for security
    const apiKey = req.headers['x-api-key'];
    if (apiKey !== process.env.CRON_API_KEY) {
      return res.status(401).json({ 
        success: false, 
        message: 'Unauthorized. Invalid API key.' 
      });
    }

    const interestService = new InterestCalculationService();
    const results = await interestService.processAllInterest();

    res.json({
      success: true,
      message: 'Daily interest processing completed',
      data: results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error processing daily interest:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/interest/history/:userId - Get user's interest earning history
router.get('/history/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const { period = 'month', page = 1, limit = 20 } = req.query;

    // Check if user can access this data (own data or admin)
    if (req.user.id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied. You can only view your own interest history.' 
      });
    }

    // Calculate date range based on period
    let startDate = new Date();
    switch (period) {
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(startDate.getMonth() - 1);
    }

    // Get interest transactions
    const interestTransactions = await Transaction.find({
      userId,
      type: 'interest',
      createdAt: { $gte: startDate }
    })
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip((parseInt(page) - 1) * parseInt(limit));

    // Get total count for pagination
    const totalTransactions = await Transaction.countDocuments({
      userId,
      type: 'interest',
      createdAt: { $gte: startDate }
    });

    // Calculate summary statistics
    const totalInterest = await Transaction.aggregate([
      {
        $match: {
          userId: userId,
          type: 'interest',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Group by subcategory for breakdown
    const interestBreakdown = await Transaction.aggregate([
      {
        $match: {
          userId: userId,
          type: 'interest',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$subcategory',
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        transactions: interestTransactions,
        summary: {
          period,
          totalInterest: totalInterest[0]?.total || 0,
          totalTransactions: totalInterest[0]?.count || 0,
          breakdown: interestBreakdown,
          averageDaily: totalInterest[0] ? 
            (totalInterest[0].total / Math.ceil((new Date() - startDate) / (1000 * 60 * 60 * 24))) : 0
        },
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalTransactions / parseInt(limit)),
          totalTransactions,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching interest history:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/interest/rates - Get current interest rates for all products
router.get('/rates', authenticateToken, async (req, res) => {
  try {
    const rates = {
      flexSavings: {
        annualRate: 15,
        dailyRate: 0.0411,
        description: 'Flexible savings with instant access',
        features: ['Instant deposits', 'Instant withdrawals', 'Daily interest', 'No lock period']
      },
      fixedDeposit: {
        rates: [
          { duration: '30-89 days', annualRate: 15, description: 'Short term fixed deposit' },
          { duration: '90-179 days', annualRate: 16, description: 'Medium term fixed deposit' },
          { duration: '180-364 days', annualRate: 17, description: 'Long term fixed deposit' },
          { duration: '365+ days', annualRate: 18, description: 'Extended term fixed deposit' }
        ],
        features: ['Guaranteed returns', 'Early termination with penalty', 'Auto-renewal option', 'Compound interest']
      },
      safeLock: {
        baseTiers: [
          { tier: 'Bronze', minAmount: 5000, minDuration: 30, baseRate: 18 },
          { tier: 'Silver', minAmount: 100000, minDuration: 90, baseRate: 19 },
          { tier: 'Gold', minAmount: 500000, minDuration: 180, baseRate: 20 },
          { tier: 'Platinum', minAmount: 1000000, minDuration: 365, baseRate: 21 }
        ],
        durationBonuses: [
          { duration: '90+ days', bonus: 1 },
          { duration: '180+ days', bonus: 2 },
          { duration: '365+ days', bonus: 4 }
        ],
        maturityBonus: 2,
        features: ['Highest interest rates', 'Complete fund lock', 'Maturity bonus', 'Emergency break option']
      },
      targetSavings: {
        annualRate: 10,
        dailyRate: 0.0274,
        description: 'Goal-oriented savings with moderate returns',
        features: ['Goal tracking', 'Flexible deposits', 'Moderate interest', 'Achievement rewards']
      }
    };

    res.json({
      success: true,
      data: {
        rates,
        lastUpdated: new Date().toISOString(),
        disclaimer: 'Interest rates are subject to change. All rates are annual percentages.'
      }
    });

  } catch (error) {
    console.error('Error fetching interest rates:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

// GET /api/interest/dashboard/:userId - Get user's interest dashboard
router.get('/dashboard/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user can access this data
    if (req.user.id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied.' 
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ 
        success: false, 
        message: 'User not found' 
      });
    }

    // Get interest earned in different periods
    const periods = ['week', 'month', 'quarter', 'year'];
    const interestByPeriod = {};

    for (const period of periods) {
      let startDate = new Date();
      switch (period) {
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
      }

      const periodInterest = await Transaction.aggregate([
        {
          $match: {
            userId: userId,
            type: 'interest',
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$subcategory',
            total: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]);

      interestByPeriod[period] = {
        total: periodInterest.reduce((sum, item) => sum + item.total, 0),
        breakdown: periodInterest
      };
    }

    // Get projected monthly interest based on current balances
    // This would require accessing current balances from different savings products
    const projectedMonthlyInterest = {
      flexSavings: 0, // Calculate based on current flex balance
      fixedDeposits: 0, // Calculate based on active fixed deposits
      safeLocks: 0, // Calculate based on active SafeLocks
      targetSavings: 0 // Calculate based on target savings balances
    };

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: `${user.firstName} ${user.lastName}`,
          totalEarnings: user.totalEarnings
        },
        interestByPeriod,
        projectedMonthlyInterest,
        summary: {
          lifetimeEarnings: user.totalEarnings,
          thisMonth: interestByPeriod.month.total,
          thisWeek: interestByPeriod.week.total,
          averageMonthly: interestByPeriod.year.total / 12
        }
      }
    });

  } catch (error) {
    console.error('Error fetching interest dashboard:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  }
});

module.exports = router;
