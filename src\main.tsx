
import React from 'react'
import { createRoot } from 'react-dom/client'
import { StrictMode } from 'react'
import App from './App.tsx'
import './index.css'

// Get the root element
const rootElement = document.getElementById("root");

// Check if the root element exists
if (!rootElement) {
  console.error("Root element not found!");
} else {
  const root = createRoot(rootElement);
  
  // Render with StrictMode for better development experience
  root.render(
    <StrictMode>
      <App />
    </StrictMode>
  );
}
