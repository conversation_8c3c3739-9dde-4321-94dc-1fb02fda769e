const mongoose = require('mongoose');

const ReferralSchema = new mongoose.Schema({
  referrerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  refereeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  referralCode: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'qualified', 'rewarded', 'expired'],
    default: 'pending',
  },
  // Reward amounts
  referrerReward: {
    type: Number,
    default: 500, // ₦500 for referrer
  },
  refereeReward: {
    type: Number,
    default: 200, // ₦200 for referee
  },
  // Qualification tracking
  qualificationCriteria: {
    kycCompleted: {
      type: Boolean,
      default: false,
    },
    minimumSavings: {
      type: Number,
      default: 5000, // ₦5,000 minimum savings
    },
    currentSavings: {
      type: Number,
      default: 0,
    },
    savingsAchieved: {
      type: Boolean,
      default: false,
    },
    daysActive: {
      type: Number,
      default: 0,
    },
    minimumActiveDays: {
      type: Number,
      default: 7, // 7 days minimum activity
    },
    activityAchieved: {
      type: Boolean,
      default: false,
    }
  },
  // Important dates
  qualificationDate: {
    type: Date,
    default: null,
  },
  rewardPaidDate: {
    type: Date,
    default: null,
  },
  expiryDate: {
    type: Date,
    default: null,
  },
  // Fraud prevention
  ipAddress: {
    type: String,
    default: null,
  },
  deviceFingerprint: {
    type: String,
    default: null,
  },
  isSuspicious: {
    type: Boolean,
    default: false,
  },
  suspiciousReasons: [{
    type: String
  }],
  // Tier system
  referrerTier: {
    type: String,
    enum: ['bronze', 'silver', 'gold', 'platinum'],
    default: 'bronze',
  },
  tierMultiplier: {
    type: Number,
    default: 1,
  },
  // Transaction references
  referrerTransactionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Transaction',
    default: null,
  },
  refereeTransactionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Transaction',
    default: null,
  },
}, { timestamps: true });

// Indexes for efficient queries
ReferralSchema.index({ referrerId: 1 });
ReferralSchema.index({ refereeId: 1 });
ReferralSchema.index({ referralCode: 1 });
ReferralSchema.index({ status: 1 });
ReferralSchema.index({ createdAt: 1 });

// Pre-save middleware to set expiry date
ReferralSchema.pre('save', function(next) {
  if (this.isNew && !this.expiryDate) {
    // Set expiry to 30 days from creation
    this.expiryDate = new Date(Date.now() + (30 * 24 * 60 * 60 * 1000));
  }
  next();
});

// Method to check if referral has expired
ReferralSchema.methods.hasExpired = function() {
  return new Date() > this.expiryDate;
};

// Method to check qualification status
ReferralSchema.methods.checkQualification = async function() {
  if (this.status !== 'pending' || this.hasExpired()) {
    return false;
  }

  const User = mongoose.model('User');
  const referee = await User.findById(this.refereeId);
  
  if (!referee) {
    return false;
  }

  // Check KYC completion
  this.qualificationCriteria.kycCompleted = referee.kycStatus === 'APPROVED';

  // Check savings amount
  this.qualificationCriteria.currentSavings = referee.savingsBalance || 0;
  this.qualificationCriteria.savingsAchieved = 
    this.qualificationCriteria.currentSavings >= this.qualificationCriteria.minimumSavings;

  // Check activity days
  const daysSinceRegistration = Math.floor(
    (new Date() - referee.createdAt) / (1000 * 60 * 60 * 24)
  );
  this.qualificationCriteria.daysActive = daysSinceRegistration;
  this.qualificationCriteria.activityAchieved = 
    daysSinceRegistration >= this.qualificationCriteria.minimumActiveDays;

  // Check if all criteria are met
  const isQualified = 
    this.qualificationCriteria.kycCompleted &&
    this.qualificationCriteria.savingsAchieved &&
    this.qualificationCriteria.activityAchieved;

  if (isQualified && this.status === 'pending') {
    this.status = 'qualified';
    this.qualificationDate = new Date();
  }

  await this.save();
  return isQualified;
};

// Method to process reward payment
ReferralSchema.methods.processReward = async function() {
  if (this.status !== 'qualified') {
    throw new Error('Referral is not qualified for reward');
  }

  const User = mongoose.model('User');
  const Transaction = mongoose.model('Transaction');

  // Get referrer and referee
  const referrer = await User.findById(this.referrerId);
  const referee = await User.findById(this.refereeId);

  if (!referrer || !referee) {
    throw new Error('Referrer or referee not found');
  }

  // Calculate tier-based rewards
  const finalReferrerReward = this.referrerReward * this.tierMultiplier;
  const finalRefereeReward = this.refereeReward;

  // Update user balances
  referrer.balance += finalReferrerReward;
  referrer.referralEarnings += finalReferrerReward;
  await referrer.save();

  referee.balance += finalRefereeReward;
  await referee.save();

  // Create transaction records
  const referrerTransaction = new Transaction({
    userId: this.referrerId,
    type: 'referral_bonus',
    amount: finalReferrerReward,
    description: `Referral bonus for inviting ${referee.firstName} ${referee.lastName}`,
    reference: `REF_BONUS_${this._id}_REFERRER`,
    category: 'referral',
    subcategory: 'referrer_bonus',
    paymentMethod: 'internal',
    balanceAfter: referrer.balance,
  });

  const refereeTransaction = new Transaction({
    userId: this.refereeId,
    type: 'referral_bonus',
    amount: finalRefereeReward,
    description: `Welcome bonus for joining via referral`,
    reference: `REF_BONUS_${this._id}_REFEREE`,
    category: 'referral',
    subcategory: 'referee_bonus',
    paymentMethod: 'internal',
    balanceAfter: referee.balance,
  });

  await referrerTransaction.save();
  await refereeTransaction.save();

  // Update referral status
  this.status = 'rewarded';
  this.rewardPaidDate = new Date();
  this.referrerTransactionId = referrerTransaction._id;
  this.refereeTransactionId = refereeTransaction._id;

  await this.save();

  return {
    referrerReward: finalReferrerReward,
    refereeReward: finalRefereeReward,
    referrerTransaction: referrerTransaction._id,
    refereeTransaction: refereeTransaction._id
  };
};

// Method to detect suspicious activity
ReferralSchema.methods.detectSuspiciousActivity = async function() {
  const suspiciousReasons = [];

  // Check for same IP address
  if (this.ipAddress) {
    const sameIpReferrals = await mongoose.model('Referral').countDocuments({
      ipAddress: this.ipAddress,
      _id: { $ne: this._id },
      createdAt: { $gte: new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)) } // Last 7 days
    });

    if (sameIpReferrals > 2) {
      suspiciousReasons.push('Multiple referrals from same IP address');
    }
  }

  // Check for same device fingerprint
  if (this.deviceFingerprint) {
    const sameDeviceReferrals = await mongoose.model('Referral').countDocuments({
      deviceFingerprint: this.deviceFingerprint,
      _id: { $ne: this._id },
      createdAt: { $gte: new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)) }
    });

    if (sameDeviceReferrals > 1) {
      suspiciousReasons.push('Multiple referrals from same device');
    }
  }

  // Check for rapid qualification (too fast)
  if (this.qualificationDate) {
    const qualificationTime = this.qualificationDate - this.createdAt;
    const hoursToQualify = qualificationTime / (1000 * 60 * 60);

    if (hoursToQualify < 1) {
      suspiciousReasons.push('Qualified too quickly (less than 1 hour)');
    }
  }

  // Update suspicious status
  if (suspiciousReasons.length > 0) {
    this.isSuspicious = true;
    this.suspiciousReasons = suspiciousReasons;
    await this.save();
  }

  return suspiciousReasons;
};

// Static method to get referrer tier based on successful referrals
ReferralSchema.statics.calculateReferrerTier = async function(referrerId) {
  const successfulReferrals = await this.countDocuments({
    referrerId,
    status: 'rewarded'
  });

  let tier = 'bronze';
  let multiplier = 1;

  if (successfulReferrals >= 50) {
    tier = 'platinum';
    multiplier = 2.5;
  } else if (successfulReferrals >= 20) {
    tier = 'gold';
    multiplier = 2;
  } else if (successfulReferrals >= 10) {
    tier = 'silver';
    multiplier = 1.5;
  }

  return { tier, multiplier };
};

// Static method to get referrals for processing
ReferralSchema.statics.getReferralsForProcessing = function() {
  return this.find({
    status: 'pending',
    expiryDate: { $gt: new Date() }
  }).populate('referrerId refereeId');
};

// Static method to get user's referral stats
ReferralSchema.statics.getUserReferralStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { referrerId: userId } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalReward: { $sum: '$referrerReward' }
      }
    }
  ]);

  const { tier, multiplier } = await this.calculateReferrerTier(userId);

  return {
    stats,
    tier,
    multiplier,
    totalReferrals: stats.reduce((sum, stat) => sum + stat.count, 0),
    totalEarnings: stats
      .filter(stat => stat._id === 'rewarded')
      .reduce((sum, stat) => sum + stat.totalReward, 0)
  };
};

module.exports = mongoose.model('Referral', ReferralSchema);
