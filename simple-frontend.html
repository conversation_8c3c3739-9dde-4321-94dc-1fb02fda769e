<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KojaPay Savings App</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            margin-top: 10px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .error {
            color: #ff6b6b;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .success {
            color: #51cf66;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tab {
            flex: 1;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
        }
        
        .tab.active {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .status-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            margin: 10px 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-online {
            background: #4CAF50;
        }
        
        .status-offline {
            background: #f44336;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;
        const API_URL = 'http://localhost:3001/api';

        function App() {
            const [activeTab, setActiveTab] = useState('login');
            const [backendStatus, setBackendStatus] = useState('checking');
            const [loginData, setLoginData] = useState({ email: '', password: '' });
            const [registerData, setRegisterData] = useState({
                firstName: '',
                lastName: '',
                email: '',
                password: '',
                phone: ''
            });
            const [loading, setLoading] = useState(false);
            const [message, setMessage] = useState('');
            const [user, setUser] = useState(null);

            useEffect(() => {
                checkBackendStatus();
                checkExistingAuth();
            }, []);

            const checkBackendStatus = async () => {
                try {
                    const response = await fetch(`${API_URL}/health`);
                    if (response.ok) {
                        setBackendStatus('online');
                    } else {
                        setBackendStatus('offline');
                    }
                } catch (error) {
                    setBackendStatus('offline');
                }
            };

            const checkExistingAuth = () => {
                const token = localStorage.getItem('token');
                const userData = localStorage.getItem('user');
                if (token && userData) {
                    try {
                        setUser(JSON.parse(userData));
                    } catch (error) {
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                }
            };

            const handleLogin = async (e) => {
                e.preventDefault();
                setLoading(true);
                setMessage('');

                try {
                    const response = await fetch(`${API_URL}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(loginData),
                    });

                    const data = await response.json();

                    if (response.ok) {
                        localStorage.setItem('token', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        setUser(data.user);
                        setMessage('Login successful!');
                    } else {
                        setMessage(data.message || 'Login failed');
                    }
                } catch (error) {
                    setMessage('Network error: ' + error.message);
                } finally {
                    setLoading(false);
                }
            };

            const handleRegister = async (e) => {
                e.preventDefault();
                setLoading(true);
                setMessage('');

                try {
                    const response = await fetch(`${API_URL}/auth/register`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(registerData),
                    });

                    const data = await response.json();

                    if (response.ok) {
                        localStorage.setItem('token', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        setUser(data.user);
                        setMessage('Registration successful!');
                    } else {
                        setMessage(data.message || 'Registration failed');
                    }
                } catch (error) {
                    setMessage('Network error: ' + error.message);
                } finally {
                    setLoading(false);
                }
            };

            const handleLogout = () => {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                setUser(null);
                setMessage('Logged out successfully');
            };

            if (user) {
                return (
                    <div className="container">
                        <div className="header">
                            <div className="logo">💰 KojaPay</div>
                            <div className="subtitle">Welcome back, {user.firstName}!</div>
                        </div>

                        <div className="card">
                            <h2>Dashboard</h2>
                            <div className="status-card">
                                <div>
                                    <strong>Account Balance</strong>
                                    <div>₦{user.balance?.toLocaleString() || '0'}</div>
                                </div>
                            </div>
                            <div className="status-card">
                                <div>
                                    <strong>Savings Balance</strong>
                                    <div>₦{user.savingsBalance?.toLocaleString() || '0'}</div>
                                </div>
                            </div>
                            <div className="status-card">
                                <div>
                                    <strong>Total Earnings</strong>
                                    <div>₦{user.totalEarnings?.toLocaleString() || '0'}</div>
                                </div>
                            </div>
                            <div className="status-card">
                                <div>
                                    <strong>KYC Status</strong>
                                    <div>{user.kycStatus || 'NOT_SUBMITTED'}</div>
                                </div>
                            </div>
                            <button onClick={handleLogout}>Logout</button>
                        </div>
                    </div>
                );
            }

            return (
                <div className="container">
                    <div className="header">
                        <div className="logo">💰 KojaPay</div>
                        <div className="subtitle">Your Smart Savings Partner</div>
                    </div>

                    <div className="status-card">
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                            <div className={`status-indicator ${backendStatus === 'online' ? 'status-online' : 'status-offline'}`}></div>
                            <span>Backend Status: {backendStatus}</span>
                        </div>
                        <button onClick={checkBackendStatus}>Refresh</button>
                    </div>

                    <div className="card">
                        <div className="tabs">
                            <button 
                                className={`tab ${activeTab === 'login' ? 'active' : ''}`}
                                onClick={() => setActiveTab('login')}
                            >
                                Login
                            </button>
                            <button 
                                className={`tab ${activeTab === 'register' ? 'active' : ''}`}
                                onClick={() => setActiveTab('register')}
                            >
                                Register
                            </button>
                        </div>

                        {activeTab === 'login' ? (
                            <form onSubmit={handleLogin}>
                                <div className="form-group">
                                    <label>Email</label>
                                    <input
                                        type="email"
                                        value={loginData.email}
                                        onChange={(e) => setLoginData({...loginData, email: e.target.value})}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label>Password</label>
                                    <input
                                        type="password"
                                        value={loginData.password}
                                        onChange={(e) => setLoginData({...loginData, password: e.target.value})}
                                        required
                                    />
                                </div>
                                <button type="submit" disabled={loading}>
                                    {loading ? 'Logging in...' : 'Login'}
                                </button>
                            </form>
                        ) : (
                            <form onSubmit={handleRegister}>
                                <div className="form-group">
                                    <label>First Name</label>
                                    <input
                                        type="text"
                                        value={registerData.firstName}
                                        onChange={(e) => setRegisterData({...registerData, firstName: e.target.value})}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label>Last Name</label>
                                    <input
                                        type="text"
                                        value={registerData.lastName}
                                        onChange={(e) => setRegisterData({...registerData, lastName: e.target.value})}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label>Email</label>
                                    <input
                                        type="email"
                                        value={registerData.email}
                                        onChange={(e) => setRegisterData({...registerData, email: e.target.value})}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label>Phone (Optional)</label>
                                    <input
                                        type="tel"
                                        value={registerData.phone}
                                        onChange={(e) => setRegisterData({...registerData, phone: e.target.value})}
                                    />
                                </div>
                                <div className="form-group">
                                    <label>Password</label>
                                    <input
                                        type="password"
                                        value={registerData.password}
                                        onChange={(e) => setRegisterData({...registerData, password: e.target.value})}
                                        required
                                    />
                                </div>
                                <button type="submit" disabled={loading}>
                                    {loading ? 'Creating account...' : 'Create Account'}
                                </button>
                            </form>
                        )}

                        {message && (
                            <div className={message.includes('successful') ? 'success' : 'error'}>
                                {message}
                            </div>
                        )}
                    </div>

                    <div className="card">
                        <h3>Quick Test</h3>
                        <p>Try the default admin login:</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Password:</strong> KojaPay2024Admin!</p>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
