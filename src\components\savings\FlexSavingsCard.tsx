import { Button } from '@/components/ui/button';
import { FintechCard } from '@/components/ui/fintech-card';
import { FloatingLabelInput } from '@/components/ui/floating-label-input';
import { useSavingsApi } from '@/hooks/use-savings-api';
import { useToast } from '@/hooks/use-toast';
import { Minus, Plus, TrendingUp, Wallet } from 'lucide-react';
import { useEffect, useState } from 'react';

const FlexSavingsCard = () => {
  const [amount, setAmount] = useState('');
  const [flexAccount, setFlexAccount] = useState<any>(null);
  const { toast } = useToast();
  const {
    getFlexAccount,
    depositToFlex,
    withdrawFromFlex,
    isLoading,
    isSaving
  } = useSavingsApi();

  // Load flex account data on component mount
  useEffect(() => {
    loadFlexAccount();
  }, []);

  const loadFlexAccount = async () => {
    const result = await getFlexAccount();
    if (result.data) {
      setFlexAccount(result.data.account);
    }
  };

  const balance = flexAccount?.balance || 0;
  const dailyInterest = flexAccount?.dailyInterestRate || 0.0411;
  const todaysEarning = balance * (dailyInterest / 100);

  const handleDeposit = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid amount",
        variant: "destructive",
      });
      return;
    }

    const result = await depositToFlex(parseFloat(amount));
    if (result.data) {
      toast({
        title: "Deposit Successful!",
        description: `₦${parseFloat(amount).toLocaleString()} added to your Flex Savings.`,
      });
      setAmount('');
      loadFlexAccount(); // Refresh account data
    }
  };

  const handleWithdraw = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid amount",
        variant: "destructive",
      });
      return;
    }

    if (parseFloat(amount) > balance) {
      toast({
        title: "Insufficient Balance",
        description: "You don't have enough balance for this withdrawal",
        variant: "destructive",
      });
      return;
    }

    const result = await withdrawFromFlex(parseFloat(amount));
    if (result.data) {
      toast({
        title: "Withdrawal Successful!",
        description: `₦${parseFloat(amount).toLocaleString()} withdrawn from your Flex Savings.`,
      });
      setAmount('');
      loadFlexAccount(); // Refresh account data
    }
  };

  return (
    <FintechCard variant="glassmorphic" className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <Wallet className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h2 className="text-xl font-semibold">Flex Savings</h2>
          <p className="text-sm text-muted-foreground">Save with daily interest</p>
        </div>
      </div>

      <div className="space-y-4 mb-6">
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Current Balance</p>
          <p className="text-3xl font-bold">₦{balance.toLocaleString()}</p>
        </div>

        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-1">
            <TrendingUp className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">Today's Earning</span>
          </div>
          <p className="text-lg font-semibold text-green-600">
            +₦{todaysEarning.toFixed(2)}
          </p>
          <p className="text-xs text-green-600/80">15% annual rate</p>
        </div>
      </div>

      <div className="space-y-4">
        <FloatingLabelInput
          id="amount"
          type="number"
          label="Amount (₦)"
          value={amount}
          onChange={(e) => setAmount(e.target.value)}
          placeholder="1,000"
        />

        <div className="grid grid-cols-2 gap-3">
          <Button
            variant="outline"
            onClick={handleDeposit}
            disabled={isSaving || !amount || isLoading}
            className="border-primary text-primary hover:bg-primary hover:text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            {isSaving ? 'Processing...' : 'Deposit'}
          </Button>
          <Button
            variant="outline"
            onClick={handleWithdraw}
            disabled={isSaving || !amount || parseFloat(amount || '0') > balance || isLoading}
            className="border-gray-300 text-gray-600 hover:bg-gray-100"
          >
            <Minus className="mr-2 h-4 w-4" />
            {isSaving ? 'Processing...' : 'Withdraw'}
          </Button>
        </div>
      </div>
    </FintechCard>
  );
};

export default FlexSavingsCard;
